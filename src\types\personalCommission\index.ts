export interface ICommissionResponse {
  items: ICommissionItem[];
  totalRevenue: number;
  totalAdvance: number;
  totalIncome: number;
}

export interface ICommissionItem {
  _id: string;
  index: number;
  commission: {
    name: string;
    period: string;
    code: string;
    id: string;
  };
  escrowDate: string;
  submitDate: string;
  transactionPhase: number;
  transactionPrice: number;
  vatRate: number;
  propertyUnit: { view1: string; price: number };
  customer: {
    name: string;
  };
  commissionRevenue: number;
  commissionReceived: number;
  isPublish: boolean;
  supportName: string | null;
  exchangeName: string | null;
  source: string;
  employees: IEmployees;
  modifiedBy: string;
  createdBy: string;
  code: string;
  id: string;
  modifiedDate: string;
  createdDate: string;
  __v: number;
  test: string;
  empRevenue: number;
  roleOrder: number;
}

export interface IEmployees {
  revenueRate: number;
  commissions: IEmployeeCommissions;
  role: string;
  code: string;
  name: string;
  id: string;
}
export interface IEmployeeCommissions {
  revenue: number;
  rate: number;
  bonus: number;
  advance: number | null;
  totalExpectedIncome: number;
  revenueReceived: number;
  revenueRemain: number;
  totalRevenue: number;
  totalReceived: number;
}

export interface IFilterPersonalCommission {
  search?: string;
  periodName?: string;
  year?: string;
  period?: string;
}
