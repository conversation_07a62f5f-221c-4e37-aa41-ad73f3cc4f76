import React, { useState, useEffect } from 'react';
import { Row, Col, Typography, Button } from 'antd';
import TableComponent from '../../../../components/table';
import FilterAddProduct, { FilterValues } from './filterMoreProduct';
import { useFetch } from '../../../../hooks';
import { getListEscrowProduct } from '../../../../service/depositContract';
import { EscrowContract } from '../../../../types/depositContract';

const { Title } = Typography;

interface EscrowProductProps {
  selectedProducts: any[];
  setSelectedProducts: React.Dispatch<React.SetStateAction<any[]>>;
  selectedPropertyUnitIds: string[];
  setSelectedPropertyUnitIds: React.Dispatch<React.SetStateAction<string[]>>;
  removedProductInfo: { salesProgramId: string; timestamp: number } | null;
  setRemovedProductInfo: React.Dispatch<React.SetStateAction<{ salesProgramId: string; timestamp: number } | null>>;
  initialValues?: any;
}

const EscrowProduct: React.FC<EscrowProductProps> = ({
  selectedProducts,
  setSelectedProducts,
  setSelectedPropertyUnitIds,
  removedProductInfo,
  initialValues,
}) => {
  const [filterParams, setFilterParams] = useState({
    salesProgramIds: '',
    blocks: '',
    floors: '',
    rooms: '',
  });

  const [removedProductIds, setRemovedProductIds] = useState<string[]>([]);

  const { data: dataEscrowProduct } = useFetch<EscrowContract[]>({
    queryKeyArrWithFilter: ['get-list-escrow-contract', filterParams],
    api: getListEscrowProduct,
    moreParams: filterParams,
    enabled: !!filterParams.salesProgramIds,
  });

  const escrowProducts = dataEscrowProduct?.data?.data;

  const displayProducts = initialValues
    ? selectedProducts
    : escrowProducts?.filter((product: any) => !removedProductIds.includes(product.id)) || [];

  console.log(displayProducts);

  // Cập nhật selectedProducts và selectedPropertyUnitIds khi escrowProducts thay đổi
  useEffect(() => {
    if (escrowProducts && escrowProducts.length > 0) {
      if (initialValues) {
        // Khi có initialValues, thêm escrowProducts vào selectedProducts (tránh trùng lặp)
        setSelectedProducts(prevProducts => {
          const existingIds = new Set(prevProducts.map(p => p.id));
          const newProducts = escrowProducts.filter((p: EscrowContract) => !existingIds.has(p.id));
          const updatedProducts = [...prevProducts, ...newProducts];
          return updatedProducts;
        });

        // Cập nhật selectedPropertyUnitIds
        setSelectedPropertyUnitIds(prevIds => {
          const newIds = escrowProducts.map((product: any) => product.id);
          const uniqueIds = [...new Set([...prevIds, ...newIds])];
          return uniqueIds;
        });
      } else {
        const productIds = escrowProducts.map((product: any) => product.id);
        setSelectedPropertyUnitIds(productIds);
      }
    }
  }, [escrowProducts, setSelectedPropertyUnitIds, setSelectedProducts, initialValues]);

  const handleAddProducts = (values: FilterValues) => {
    // Cập nhật filter params để gọi API với các tham số mới
    const newFilterParams = {
      salesProgramIds: values.salesProgramIds?.join(',') || '',
      blocks: values.blocks?.join(',') || '',
      floors: values.floors?.join(',') || '',
      rooms: values.rooms?.join(',') || '',
    };

    setFilterParams(newFilterParams);
  };

  const removeProduct = (productId: string) => {
    if (initialValues) {
      // Khi có initialValues (edit mode), xóa từ selectedProducts
      setSelectedProducts(prev => {
        const updated = prev.filter(p => p.id !== productId);
        return updated;
      });

      // Xóa productId khỏi selectedPropertyUnitIds
      setSelectedPropertyUnitIds(prev => {
        const updated = prev.filter(id => id !== productId);
        return updated;
      });
    } else {
      // Thêm productId vào danh sách removed
      setRemovedProductIds(prev => {
        const updated = [...prev, productId];
        return updated;
      });

      // Xóa productId khỏi selectedPropertyUnitIds
      setSelectedPropertyUnitIds(prev => {
        const updated = prev.filter(id => id !== productId);
        return updated;
      });
    }
  };

  const customComponents = {
    header: {
      row: (props: React.HTMLAttributes<HTMLTableRowElement>) => (
        <>
          <tr {...props} />
          <tr>
            <td colSpan={3} className="ant-table-cell-total-product">
              <Typography.Text className="text-total-product">
                Tổng số sản phẩm ký quỹ: {displayProducts?.length || 0}
              </Typography.Text>
            </td>
          </tr>
        </>
      ),
    },
  };

  const columnsEscrowProduct = [
    {
      title: 'Mã sản phẩm',
      dataIndex: 'code',
      key: 'code',
      width: 150,
      render: (code: string) => <div>{code}</div>,
    },
    {
      title: 'Chương trình bán hàng',
      dataIndex: 'name',
      key: 'name',
      width: 360,
      render: (_: string, record: any) => <div>{record?.salesProgram?.name}</div>,
    },
    {
      title: '',
      key: 'action',
      width: 80,
      align: 'center' as const,
      render: (_: string, record: any) => (
        <Button type="link" danger onClick={() => removeProduct(record.id)} size="small">
          Xóa
        </Button>
      ),
    },
  ];

  return (
    <div>
      <Title level={5}>Sản phẩm ký quỹ</Title>
      <Row gutter={16}>
        <Col span={24}>
          <p>
            <FilterAddProduct
              handleSubmit={handleAddProducts}
              initialSearchValues={undefined}
              removedProductInfo={removedProductInfo}
            />
          </p>
          <TableComponent
            queryKeyArr={['get-escrow-product']}
            columns={columnsEscrowProduct}
            dataSource={displayProducts}
            pagination={false}
            className="unit-conversion-table"
            components={customComponents}
            rowKey="id"
            isPagination={false}
            style={{ marginBottom: 16 }}
          />
        </Col>
      </Row>
    </div>
  );
};

export default EscrowProduct;
