.property-attribute-modal__value-list {
  overflow: hidden;
  border-bottom: 1px solid #0000000f;
}

.property-attribute-modal__value-header {
  display: flex;
  background: #fafafa;
  font-weight: 600;
  padding: 8px 16px;
}

.property-attribute-modal__value-header-name {
  flex: 1;
}

.property-attribute-modal__value-header-action {
  width: 80px;
}

.property-attribute-modal__value-row {
  display: flex;
  align-items: center;
  padding: 8px;
  border-top: 1px solid #0000000f;
}

.property-attribute-modal__value-input {
  flex: 1;
  margin-bottom: 0 !important;
}

.property-attribute-modal__value-action {
  width: 80px;
  text-align: center;
}

.property-attribute-modal__info-title {
  margin-bottom: 32px !important;
}

.property-attribute-modal__value-title {
  margin-bottom: 42px !important;
}
