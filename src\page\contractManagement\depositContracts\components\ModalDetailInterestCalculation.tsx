import { <PERSON><PERSON>, Col, DatePicker, Form, Input, Modal, Row, Typography } from 'antd';
import { useEffect, useState } from 'react';
import './styles.scss';
import React from 'react';
import { QueryKey } from '@tanstack/react-query';
import ModalComponent from '../../../../components/modal';
import dayjs from 'dayjs';
import { FORMAT_DATE_TIME, OPTIONS_TYPE_PROPOSAL_INTEREST_CALCULATION } from '../../../../constants/common';
import { useFetch, useUpdateField } from '../../../../hooks';
import { approveInterestCalculation, getDetailInterestCalculation } from '../../../../service/contract';
import { useParams } from 'react-router-dom';
import { TInterestCalculation } from '../../../../types/contract/depositContract';
import ModalConfirmCreateProposal from './ModalConfirmCreateProposal';
import CreateModalProposalNoneTemplate from '../../../proposalManagement/createProposalNoneTemplate';
import { TCreateProposal } from '../../../../types/proposal';

const { Item } = Form;
const { Title, Text } = Typography;

interface ModalDetailInterestCalculationProps {
  data?: TInterestCalculation;
  visible: boolean;
  onClose: () => void;
  keyQuery?: QueryKey;

  isChecked?: boolean;
  setIsChecked?: React.Dispatch<React.SetStateAction<boolean>>;
  toggleOpenModalCreateInterestReduction?: () => void;
}

const ModalDetailInterestCalculation = ({
  data,
  isChecked,
  visible,
  onClose,

  setIsChecked,
}: ModalDetailInterestCalculationProps) => {
  const { id: idContract } = useParams();

  const [form] = Form.useForm();

  const [initialValues, setInitialValues] = useState<TInterestCalculation>();

  const [initValueCreateProposal, setInitValueCreateProposal] = useState<TCreateProposal>();

  const [isOpenModalCreateProposal, setIsOpenModalCreateProposal] = useState<boolean>(false);
  const [isOpenModalCreateInterestReduction, setIsOpenModalCreateInterestReduction] = useState<boolean>(false);

  const toggleOpenModalCreateProposal = () => setIsOpenModalCreateProposal(!isOpenModalCreateProposal);
  const toggleOpenModalCreateInterestReduction = () =>
    setIsOpenModalCreateInterestReduction(!isOpenModalCreateInterestReduction);

  const { data: dataDetail } = useFetch<TInterestCalculation>({
    api: getDetailInterestCalculation,
    queryKeyArr: ['get-detail-interestCalculation', data?.id],
    moreParams: { idContract: idContract, id: data?.id },
    enabled: !!data?.id,
    cacheTime: 10,
  });
  const detailInterestCalculation = dataDetail?.data?.data;

  const [isModified, setIsModified] = useState(false);

  const { mutateAsync: mutateApprove } = useUpdateField({
    apiQuery: approveInterestCalculation,
    keyOfListQuery: ['get-detail-interestCalculation'],
    keyOfDetailQuery: ['get-detail-interestCalculation', data?.id],
  });

  //   const { data: dataProposalType } = useFetch<TProposalType[]>({
  //     queryKeyArrWithFilter: ['get-proposal-type'],
  //     api: getProposalType,
  //   });

  // cảnh báo khi thoát trang khi chưa lưu dữ liệu
  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  const handleOpenModalCreateProposal = (proposalType: string) => {
    setInitValueCreateProposal({
      type: proposalType,
      contract: { ...form?.getFieldsValue(), contractId: idContract, interestReductionAmountEap: '' },
    });
    toggleOpenModalCreateProposal();
  };

  const handleAfterSubmitProposal = () => {
    if (isOpenModalCreateInterestReduction) toggleOpenModalCreateInterestReduction();
  };

  const handleCancelProposal = () => {
    if (isOpenModalCreateInterestReduction) toggleOpenModalCreateInterestReduction();
  };

  const handleCancel = React.useCallback(() => {
    if (form.isFieldsTouched()) {
      Modal.confirm({
        title: 'Xác nhận hủy',
        content: 'Dữ liệu đang chưa được lưu, bạn có chắc muốn hủy dữ liệu đang nhập không?',
        cancelText: 'Quay lại',
        onOk: () => {
          form.resetFields();
          onClose();
        },
        okButtonProps: {
          type: 'default',
        },
        cancelButtonProps: {
          type: 'primary',
        },
      });
      setIsModified(false);
    } else {
      form.resetFields();
      setIsModified(false);
      onClose();
    }
  }, [form, onClose]);

  const handleFinish = async () => {
    const transformedValues = {
      id: idContract,
      interestCalculation: {
        id: data?.id,
      },
    };
    try {
      const res = await mutateApprove(transformedValues);
      const statusCode = res?.data?.statusCode;
      if (statusCode === '0') {
        setIsModified(false);
        onClose();
      }
    } catch (error: unknown) {
      //   console.log(error);
    }
  };

  const validateForm = () => {
    setIsModified(true);
  };

  useEffect(() => {
    if (detailInterestCalculation) {
      const initialData = {
        ...detailInterestCalculation,
      };

      setInitialValues(initialData as TInterestCalculation);
      form.setFieldsValue(initialData);
    }
  }, [data, detailInterestCalculation, form]);

  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  return (
    <>
      <ModalComponent
        title="Chi tiết phiếu công nợ"
        open={visible}
        onCancel={handleCancel}
        destroyOnClose
        footer={
          <>
            <Button type="default" htmlType="submit" onClick={() => form.submit()}>
              Xác nhận
            </Button>
            <Button type="default" htmlType="submit" onClick={toggleOpenModalCreateInterestReduction}>
              Xin giảm lãi
            </Button>
            <Button type="primary" htmlType="submit" onClick={() => form.submit()}>
              Lưu
            </Button>
          </>
        }
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleFinish}
          onValuesChange={validateForm}
          initialValues={initialValues}
          className="space-y-6"
        >
          <Row gutter={{ md: 24, lg: 40, xl: 80, xxl: 126 }}>
            <Col xs={24} md={12}>
              <Title level={5}>Thông tin chung</Title>
              <Row gutter={{ md: 24, lg: 40 }}>
                <Col xs={24} md={24}>
                  <Item label="Mã phiếu tính lãi" name="code">
                    <Input placeholder="Nhập mã phiếu tính lãi" maxLength={255} disabled />
                  </Item>
                </Col>

                <Col xs={24} md={24}>
                  <Item label="Tiêu đề" name="title">
                    <Input placeholder="Nhập tiêu đề" maxLength={255} disabled />
                  </Item>
                </Col>

                <Col xs={24} md={24}>
                  <Item label="Khách hàng" name="customerName">
                    <Input placeholder="Nhập tên khách hàng" maxLength={255} disabled />
                  </Item>
                </Col>

                <Col xs={24} md={24}>
                  <Item label="Nhóm tuổi nợ" name="debtage">
                    <Input placeholder="Nhập nhóm tuổi nợ" maxLength={255} disabled />
                  </Item>
                </Col>

                <Col xs={24} md={24}>
                  <Item label="Loại nợ" name="debtType">
                    <Input placeholder="Nhập loại nợ" maxLength={255} disabled />
                  </Item>
                </Col>

                <Col xs={24} md={24}>
                  <Item label="Lãi suất" name="interestRate">
                    <Input placeholder="Nhập lãi suất" maxLength={255} disabled suffix="%" />
                  </Item>
                </Col>

                <Col xs={24} md={24}>
                  <Item label="Thời gian tính lãi" name={['startDate', 'endDate']}>
                    <DatePicker disabled />
                  </Item>
                </Col>

                <Col xs={24} md={24}>
                  <Item label="Số ngày trễ hạn TT" name="totalDelayDate">
                    <Input placeholder="Nhập số ngày trễ hạn" maxLength={255} disabled suffix="Ngày" />
                  </Item>
                </Col>

                <Col xs={24} md={24}>
                  <Item label="Số tiền lãi" name="interestAmount">
                    <Input placeholder="Nhập số tiền lãi" maxLength={255} disabled suffix="VNĐ" />
                  </Item>
                </Col>

                <Col xs={24} md={24}>
                  <Item label="Số tiền giảm lãi" name="interestReductionAmount">
                    <Input placeholder="Nhập số tiền giảm lãi" maxLength={255} disabled suffix="VNĐ" />
                  </Item>
                </Col>

                <Col xs={24} md={24}>
                  <Item label="Mã tờ trình giảm lãi" name="code">
                    <Input placeholder="" maxLength={255} disabled />
                  </Item>
                </Col>

                <Col xs={24} md={24}>
                  <Item label="Số tiền lãi còn lại" name="remainingAmount">
                    <Input placeholder="Nhập số tiền lãi còn lại" maxLength={255} disabled suffix="VNĐ" />
                  </Item>
                </Col>

                <Col xs={24} md={24}>
                  <Item label="Số tiền gốc" name="needTransfer">
                    <Input placeholder="Nhập số tiền gốc" maxLength={255} disabled suffix="VNĐ" />
                  </Item>
                </Col>

                <Col xs={24} md={24}>
                  <Item label="Phí phạt quá hạn" name="latePaymentFee">
                    <Input placeholder="Nhập phí phạt quá hạn" maxLength={255} disabled suffix="VNĐ" />
                  </Item>
                </Col>

                <Col xs={24} md={24}>
                  <Item label="Tổng số tiền phải TT" name="totalSettlementAmount">
                    <Input placeholder="Nhập loại nợ" maxLength={255} disabled suffix="VNĐ" />
                  </Item>
                </Col>

                <Col xs={24} md={24}>
                  <Item label="Số tiền đã thanh toán" name="interestAmountTransferred">
                    <Input placeholder="Nhập số tiền đã thanh toán" maxLength={255} disabled suffix="VNĐ" />
                  </Item>
                </Col>

                <Col xs={24} md={24}>
                  <Item label="Trạng thái phiếu" name="status">
                    <Input placeholder="Nhập loại nợ" maxLength={255} disabled />
                  </Item>
                </Col>

                <Col xs={24} md={24}>
                  <div className="info">
                    <Row gutter={24}>
                      <Col lg={6} xs={8}>
                        <Text>Ngày cập nhật: </Text>
                      </Col>
                      <Col lg={18} xs={16}>
                        <Text>
                          {dayjs(data?.updatedDate).format(FORMAT_DATE_TIME)}&nbsp;&nbsp;{' '}
                          {`${data?.updatedBy?.userName || ''} - ${data?.updatedBy?.fullName || ''}`}
                        </Text>
                      </Col>
                    </Row>
                    <Row gutter={24}>
                      <Col lg={6} xs={8}>
                        <Text>Ngày tạo: </Text>
                      </Col>
                      <Col lg={18} xs={16}>
                        <Text>
                          {dayjs(data?.createdDate).format(FORMAT_DATE_TIME)}&nbsp;&nbsp;{' '}
                          {`${data?.createdBy?.userName || ''} - ${data?.createdBy?.fullName || ''}`}
                        </Text>
                      </Col>
                    </Row>
                  </div>
                </Col>
              </Row>
            </Col>
          </Row>
        </Form>
      </ModalComponent>
      <ModalConfirmCreateProposal
        title="Tạo tờ trình giảm lãi"
        description="Xác nhận tạo tờ trình giảm lãi"
        onCancel={() => {
          setIsChecked && setIsChecked(!isChecked);
          toggleOpenModalCreateInterestReduction && toggleOpenModalCreateInterestReduction();
        }}
        open={isOpenModalCreateInterestReduction}
        onConfirm={handleOpenModalCreateProposal}
        typeProposal="PROPOSAL_FOR_INTEREST_WAIVER_OR_REDUCTION"
      />
      <CreateModalProposalNoneTemplate
        keyQuery={['get-list-proposals']}
        visible={isOpenModalCreateProposal}
        onClose={toggleOpenModalCreateProposal}
        initialValues={initValueCreateProposal}
        listProposalTypeInterestCalculation={OPTIONS_TYPE_PROPOSAL_INTEREST_CALCULATION}
        handleAfterSubmitProposal={handleAfterSubmitProposal}
        handleCancelProposal={handleCancelProposal}
      />
    </>
  );
};

export default ModalDetailInterestCalculation;
