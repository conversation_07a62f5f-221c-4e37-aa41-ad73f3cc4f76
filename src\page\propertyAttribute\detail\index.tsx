import { But<PERSON>, Form, Spin } from 'antd';
import BreadCrumbComponent from '../../../components/breadCrumb';
import PropertyAttributeForm from '../components/PropertyAttributeForm';
import { useFetch, useUpdateField } from '../../../hooks';
import { getPropertyAttributeDetail, updatePropertyAttribute } from '../../../service/propertyAttribute';
import { useNavigate, useParams } from 'react-router-dom';
import { PROPERTY_ATTRIBUTES_MANAGEMENT } from '../../../configs/path';
import { IPropertyAttribute, IPropertyAttributeFormValues } from '../../../types/propertyAttribute';

export default function PropertyAttributeDetail() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const [form] = Form.useForm<IPropertyAttributeFormValues>();
  const { data, isFetching } = useFetch<IPropertyAttribute>({
    api: () => getPropertyAttributeDetail(id!),
    queryKeyArr: ['property-attribute-detail', id],
    enabled: !!id,
    withFilter: false,
    cacheTime: 10,
  });

  const detail = data?.data?.data as IPropertyAttribute;

  const { mutateAsync: updateProperty, isPending } = useUpdateField({
    apiQuery: updatePropertyAttribute,
    keyOfListQuery: ['property-attribute'],
    messageSuccess: 'Chỉnh sửa thuộc tính thành công',
  });

  const handleFinish = async (values: IPropertyAttributeFormValues) => {
    const newValues = {
      ...values,
      name: values.name.trim(),
    };

    const res = await updateProperty({
      payload: newValues,
      id: id ?? '',
    });
    if (res?.data?.statusCode === '0') {
      navigate(PROPERTY_ATTRIBUTES_MANAGEMENT);
      form.resetFields();
    }
  };

  return (
    <Spin spinning={isFetching}>
      <BreadCrumbComponent titleBread={detail?.name} />

      {isFetching || (
        <PropertyAttributeForm
          form={form}
          onFinish={handleFinish}
          initialValues={{
            ...detail,
            projectId: detail?.project?.id || '',
            saleProgramId: detail?.saleProgram?.id || '',
          }}
        />
      )}

      <div style={{ display: 'flex', justifyContent: 'flex-end', gap: 8, marginTop: 32 }}>
        <Button type="default" onClick={() => form.resetFields()}>
          Huỷ
        </Button>
        <Button type="primary" onClick={() => form.submit()} loading={isPending}>
          Lưu thay đổi
        </Button>
      </div>
    </Spin>
  );
}
