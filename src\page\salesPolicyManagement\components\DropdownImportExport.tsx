import { DownOutlined } from '@ant-design/icons';
import { App, Button, Dropdown, Space, Typography, Upload } from 'antd';
import { RcFile } from 'antd/es/upload';
import { AxiosError } from 'axios';
import { SALES_POLICY } from '../../../configs/path';
import { useCreateField } from '../../../hooks';
import { postImportFileSalesPolicy } from '../../../service/salesPolicy';

const TemplateSalePolicy = '/assets/TemplateSalePolicy.xlsx';

const { Link } = Typography;

const DropdownImportExport = () => {
  const { notification } = App.useApp();

  const { mutateAsync: importFileSalesPolicy, isPending } = useCreateField({
    apiQuery: postImportFileSalesPolicy,
    isMessageSuccess: false,
    isMessageError: false,
  });

  const handleBeforeUpload = async (file: RcFile) => {
    const isAllowedType = file?.name.toLowerCase().endsWith('.xlsx');
    if (!isAllowedType) {
      notification.error({ message: 'File không đúng định dạng. Vui lòng sử dụng file .xlsx' });
      return Upload.LIST_IGNORE;
    }
    return true;
  };
  const items = [
    {
      key: 'download',
      label: (
        <Link download="TemplateSalePolicy" href={TemplateSalePolicy} target="_blank">
          Tải file mẫu
        </Link>
      ),
    },
    {
      key: 'import',
      label: (
        <Upload
          beforeUpload={handleBeforeUpload}
          maxCount={1}
          showUploadList={false}
          customRequest={async ({ file, onSuccess, onError }) => {
            try {
              const importResponse = await importFileSalesPolicy(file as RcFile);
              const status = importResponse?.data?.statusCode;

              const link = (
                <Link href={SALES_POLICY} target="_blank">
                  đây
                </Link>
              );

              if (status === 'PENDING') {
                notification.warning({
                  message: <>Upload đang được xử lý, xem tại {link}</>,
                });
              } else if (status === 'SUCCESS') {
                notification.success({
                  message: <>Hoàn thành upload khách hàng tiềm năng, xem tại {link}</>,
                });
              } else if (['PARTIAL_ERROR', 'ENTIRE_ERROR'].includes(status as string)) {
                notification.error({
                  message: <>Upload thất bại, xem tại {link}</>,
                });
              }

              onSuccess && onSuccess('ok');
            } catch (error: unknown) {
              onError && onError(error as AxiosError);
            }
          }}
          name="file-upload"
        >
          Nhập dữ liệu
        </Upload>
      ),
    },
  ];

  return (
    <div>
      <Dropdown menu={{ items }}>
        <Button loading={isPending}>
          <Space>
            Tải / nhập dữ liệu
            <DownOutlined />
          </Space>
        </Button>
      </Dropdown>
    </div>
  );
};

export default DropdownImportExport;
