import { IPropertyAttribute } from '../propertyAttribute';

export enum EAttributeValueType {
  EXACT = 'exact',
  RANGE = 'range',
}

export interface AttributeCombination {
  attributeId: string;
  value:
    | {
        type: EAttributeValueType.EXACT;
        data: string;
      }
    | {
        type: EAttributeValueType.RANGE;
        data: {
          min: number;
          max: number;
        };
      };
  attribute: IPropertyAttribute;
}

interface Project {
  _id: string;
  id: string;
  name: string;
}

interface SaleProgram {
  _id: string;
  id: string;
  name: string;
}

export interface IPriceCoefficients {
  _id: string;
  id: string;
  softDelete: boolean;
  name: string;
  projectId: string;
  saleProgramId: string;
  coefficient: number;
  attributeCombinations: AttributeCombination[];
  createdBy: string;
  createdDate: string;
  modifiedDate: string;
  __v: number;
  modifiedBy: string;
  project: Project;
  saleProgram: SaleProgram;
}

export interface AttributeRow {
  key: string;
  attribute: string;
  valueType: string;
  min?: number;
  max?: number;
  exactValue?: string;
}

export interface CreatePriceCoefficientFormValues {
  name: string;
  projectId: string;
  saleProgramId: string;
  coefficient: number;
  attributeCombinations: AttributeCombination[];
}
