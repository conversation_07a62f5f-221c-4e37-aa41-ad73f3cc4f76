import { Typography } from 'antd';
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { FORMAT_DATE_TIME, OPTIONS_STATUS_FILTER } from '../../../constants/common';
import { TListOfOwnershipCertificateConfig, TOrgChart } from '../../../types/ownershipCertificateConfig';

const { Text } = Typography;

export const Columns: ColumnsType<TListOfOwnershipCertificateConfig> = [
  {
    title: 'STT',
    dataIndex: 'stt',
    key: 'stt',
    width: 60,
    render: (_, __, index) => index + 1,
  },
  {
    title: 'Dự án',
    dataIndex: ['project', 'name'],
    key: 'projectName',
    width: 300,
    render: value => value || '-',
  },
  {
    title: 'Đơn vị thực hiện',
    dataIndex: 'orgCharts',
    key: 'orgCharts',
    width: 300,
    render: (value: TOrgChart[]) => value?.map(item => item?.name)?.join(',') || '-',
  },

  {
    title: 'Trạng thái',
    dataIndex: 'isActive',
    key: 'isActive',
    width: 100,
    render: value => {
      const status = OPTIONS_STATUS_FILTER.find(item => item.value === value);
      if (!status) return '-';
      return <Text style={{ color: status.color }}>{status.label}</Text>;
    },
  },
  {
    title: 'Ngày tạo',
    dataIndex: 'createdDate',
    width: 150,
    key: 'createdDate',
    render: (value, { createdBy }) =>
      value ? (
        <>
          <Text>{value ? dayjs(value).format(FORMAT_DATE_TIME) : '-'}</Text>
          <br />
          <Text>{createdBy?.userName || '-'}</Text>
        </>
      ) : (
        '-'
      ),
  },
  {
    title: 'Ngày cập nhật',
    dataIndex: 'updatedDate',
    width: 150,
    key: 'updatedDate',
    render: (value, { updatedBy }) =>
      value ? (
        <>
          <Text>{value ? dayjs(value).format(FORMAT_DATE_TIME) : '-'}</Text>
          <br />
          <Text>{updatedBy?.userName || '-'}</Text>
        </>
      ) : (
        '-'
      ),
  },
];
