import { Button, Form, Modal } from 'antd';
import { useState } from 'react';
import { useBeforeUnload } from 'react-router-dom';
import { showConfirmCancelModal } from '../../../components/modal/specials/ConfirmCancelModal';
import FormPeriodDebt from '../../../components/select/FormPeriodDebt';
import SingleSelectLazy from '../../../components/select/singleSelectLazy';
import { PERMISSION_DEBT_COMMISSION } from '../../../constants/permissions/debtCommission';
import { useCheckPermissions, useCreateField } from '../../../hooks';
import { createCommissionDebt } from '../../../service/commissionDebt';
import {
  getAllOfCommissionDebtPolicy,
  getProjectsForCommissionDebtPolicy,
} from '../../../service/commissionDebtPolicy';
import { ICommissionDebtPolicy, ProjectDebtPolicy } from '../../../types/commissionDebtPolicy';

const CreateCommissionDebt = () => {
  const [form] = Form.useForm();
  const { listCreate } = useCheckPermissions(PERMISSION_DEBT_COMMISSION);
  const [openModal, setOpenModal] = useState(false);
  const formProject = Form.useWatch('project', form);
  const period = Form.useWatch('period', form);
  const commissionPolicyCode = Form.useWatch('commissionPolicyCode', form);

  const { mutateAsync, isPending } = useCreateField({
    apiQuery: createCommissionDebt,
    keyOfListQuery: ['list-of-commission-debt-penalty'],
    isMessageError: false,
  });

  const handleSubmit = async () => {
    const allValues = form.getFieldsValue(true);
    if (!allValues) return;

    const res = await mutateAsync(allValues);
    if (res?.data?.statusCode === '0') {
      form.resetFields();
      setOpenModal(false);
    }
  };

  const handleSelectProject = (value: ProjectDebtPolicy) => {
    form.setFieldsValue({
      project: value
        ? {
            id: value.id,
            name: value.name,
            code: value.code,
          }
        : undefined,
      commissionPolicyCode: undefined,
      period: undefined,
      year: new Date().getFullYear().toString(),
    });
  };

  const handleSelectCommissionPolicy = (value: ICommissionDebtPolicy) => {
    form.setFieldsValue({
      commissionPolicyCode: value ? value.code : undefined,
    });
  };

  useBeforeUnload(event => {
    if (openModal && form.isFieldsTouched()) {
      event.preventDefault();
      return true;
    }
    return undefined;
  });

  return (
    <>
      {listCreate && (
        <Button type="primary" onClick={() => setOpenModal(true)}>
          Thêm mới
        </Button>
      )}
      <Modal
        className="wrapper-modal-create-commission"
        title="Tạo mới kỳ tính hoa hồng"
        open={openModal}
        footer={[
          <Button type="primary" loading={isPending} onClick={() => form.submit()}>
            Tạo mới
          </Button>,
        ]}
        onCancel={() => {
          if (!form.isFieldsTouched()) {
            setOpenModal(false);
            return;
          }
          showConfirmCancelModal({
            onConfirm: () => {
              form.resetFields();
              setOpenModal(false);
            },
          });
        }}
        destroyOnClose
        maskClosable={false}
        afterClose={() => form.resetFields()}
        width={645}
      >
        <Form form={form} onFinish={handleSubmit} requiredMark={false} layout="vertical">
          <Form.Item label="Chọn dự án" name="project" rules={[{ required: true, message: 'Vui lòng chọn dự án' }]}>
            <SingleSelectLazy
              gcTime={1000 * 60 * 15} // 15 phuts
              staleTime={1000 * 60 * 5} // 5 phút
              apiQuery={getProjectsForCommissionDebtPolicy}
              queryKey={['list-projects']}
              enabled={openModal}
              placeholder="Chọn dự án"
              keysLabel={['code', 'name']}
              handleSelect={handleSelectProject}
            />
          </Form.Item>

          <FormPeriodDebt
            required
            label="Kỳ tính hoa hồng công nợ"
            fieldName="project"
            clearFormValueDependency={() => form.setFieldValue('commissionPolicyCode', undefined)}
          />

          <Form.Item
            label="Bộ chỉ tiêu KPI công nợ"
            name="commissionPolicyCode"
            rules={[{ required: true, message: 'Vui lòng chọn bộ chỉ tiêu KPI công nợ' }]}
          >
            <SingleSelectLazy
              apiQuery={getAllOfCommissionDebtPolicy}
              queryKey={['list-of-commission-debt-policy']}
              enabled={openModal && !!formProject?.id && !!period}
              moreParams={{ project: formProject?.id, period: period, isActive: 1 }}
              placeholder="Chọn bộ chỉ tiêu KPI công nợ"
              keysLabel={['code', 'name']}
              handleSelect={handleSelectCommissionPolicy}
              disabled={!formProject?.id || !period}
              defaultValues={{
                value: commissionPolicyCode?.id,
                label: commissionPolicyCode?.name,
              }}
            />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default CreateCommissionDebt;
