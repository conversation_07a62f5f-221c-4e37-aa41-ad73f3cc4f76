import { MutationFunction } from '@tanstack/react-query';
import { App, But<PERSON>, Flex } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useMemo, useState } from 'react';
import ConfirmDeleteModal from '../../../components/modal/specials/ConfirmDeleteModal';
import { modalConfirm } from '../../../components/modal/specials/ModalConfirm';
import TableComponent from '../../../components/table';
import { ActionsColumns } from '../../../components/table/components/ActionsColumns';
import { DEBT_COMMISSION_PERIOD } from '../../../configs/path';
import { useCheckPermissions, useUpdateField } from '../../../hooks';

import { PERMISSION_DEBT_COMMISSION } from '../../../constants/permissions/debtCommission';
import {
  changeStatusTimeConfigDebtCommission,
  softDeleteTimeConfigDebtCommission,
} from '../../../service/timeConfigCommissionDebt';
import { TListOfTimeConfigDebtCommission, TTabTimeConfigDebtCommission } from '../../../types/timeConfigCommisionDebt';
import CreateTimeConfigDebtCommission from '../createTimeConfigFeeCommission';
import { useStoreTimeConfigDebtCommission } from '../storeTimeConfigDebt';
import FilterTimeConfigDebtCommission from './FilterTimeConfigFeeCommission';

interface TTimeConfigDebtCommission {
  tabActive: TTabTimeConfigDebtCommission;
  columns: ColumnsType<TListOfTimeConfigDebtCommission>;
}

const ConfigDebtCommission = (props: TTimeConfigDebtCommission) => {
  const { tabActive, columns } = props;
  const { createPeriod, deletePeriod, changeStatusPeriod, getPeriod } = useCheckPermissions(PERMISSION_DEBT_COMMISSION);

  const { modal, notification } = App.useApp();
  const { dataTimeConfig, loading, tab, getCurrentFilter } = useStoreTimeConfigDebtCommission();
  const [open, setOpen] = useState(false);
  const [isOpenModalDelete, setIsOpenModalDelete] = useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = useState<TListOfTimeConfigDebtCommission>();

  const { mutateAsync } = useUpdateField({
    apiQuery: changeStatusTimeConfigDebtCommission,
    keyOfListQuery: ['list-of-time-config-Debt-commission', getCurrentFilter()],
    isMessageError: false,
    isMessageSuccess: false,
  });

  const columnsActions: ColumnsType<TListOfTimeConfigDebtCommission> = useMemo(() => {
    return [
      ...columns,
      {
        title: 'Hành động',
        dataIndex: 'action',
        key: 'action',
        width: '100px',
        align: 'center',
        render: (_: unknown, record: TListOfTimeConfigDebtCommission) => {
          const textModalConfirmActive = record?.isActive === 1 ? 'Vô hiệu hoá' : 'Kích hoạt';
          const handleActiveTimeConfig = () => {
            return modalConfirm({
              modal: modal,
              title: `${textModalConfirmActive} cấu hình`,
              content: `Bạn có muốn ${textModalConfirmActive} cấu hình này không?`,
              handleConfirm: async () => {
                const res = await mutateAsync({
                  id: record?.id,
                  isActive: record?.isActive === 1 ? 2 : 1,
                  name: record?.name,
                });
                if (res?.data?.statusCode === '0') {
                  notification.success({
                    message: `${textModalConfirmActive} cấu hình thành công`,
                  });
                }
              },
            });
          };
          const handleDeleteRole = () => {
            setIsOpenModalDelete(true);
            setCurrentRecord(record);
          };

          const openViewDetail = () => {
            window.open(`${DEBT_COMMISSION_PERIOD}/${record?.id}?tab=${tab}`, '_blank', 'noopener,noreferrer');
          };

          return (
            <ActionsColumns
              handleViewDetail={getPeriod ? openViewDetail : undefined}
              textModalConfirmActive={textModalConfirmActive}
              handleActive={changeStatusPeriod ? handleActiveTimeConfig : undefined}
              handleDelete={deletePeriod ? handleDeleteRole : undefined}
            />
          );
        },
      },
    ];
  }, [columns, getPeriod, changeStatusPeriod, deletePeriod, modal, mutateAsync, notification, tab]);

  const handleOpenModalCreate = () => {
    setOpen(true);
  };

  const handleCloseModalCreate = () => {
    setOpen(false);
  };

  return (
    <>
      <Flex justify="space-between" className="header-filter" style={{ marginBottom: 16 }}>
        <FilterTimeConfigDebtCommission />
        {createPeriod && (
          <Button type="primary" onClick={handleOpenModalCreate}>
            Tạo mới
          </Button>
        )}
      </Flex>
      <TableComponent
        className="table-time-config-default-list"
        columns={columnsActions}
        queryKeyArr={['list-of-time-config-Debt-commission', getCurrentFilter()]}
        dataSource={dataTimeConfig[tabActive]}
        loading={loading}
        rowKey={'id'}
        heightIsSubtractedScroll={250}
      />
      <CreateTimeConfigDebtCommission open={open} handleCancel={handleCloseModalCreate} />
      <ConfirmDeleteModal
        label="Cấu hình"
        open={isOpenModalDelete}
        apiQuery={softDeleteTimeConfigDebtCommission as MutationFunction<unknown, unknown>}
        keyOfListQuery={['list-of-time-config-Debt-commission', getCurrentFilter()]}
        onCancel={() => setIsOpenModalDelete(false)}
        idDetail={currentRecord?.id}
        title="Xóa cấu hình"
        description="Vui lòng nhập lý do muốn xoá cấu hình này"
        fieldNameReason="reasonDelete"
      />
    </>
  );
};

export default ConfigDebtCommission;
