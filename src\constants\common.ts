/* eslint-disable @typescript-eslint/no-duplicate-enum-values */
import { SelectProps } from 'antd';
import { WorkingTimeConvert } from '../types/project/project';

export const DEFAULT_PARAMS = {
  page: '1',
  pageSize: '10',
};
export const DEFAULT_PARAMS_PAGESIZE = 10;
export const OPTIONS_STATES: { label: string; value: boolean }[] = [
  { label: 'Đã kích hoạt', value: true },
  { label: 'Chưa kích hoạt', value: false },
];
export const OPTIONS_POSITION = [
  { label: 'Admin', value: 'admin' },
  { label: 'User', value: 'user' },
];
export const FORMAT_DATE = 'DD/MM/YYYY';
export const FORMAT_DATE_TIME = 'DD/MM/YYYY HH:mm';
export const FORMAT_TIME = 'HH:mm';
export const FORMAT_DATE_API = 'YYYY-MM-DD';
export const FORMAT_DATE_TIME_API = 'YYYY-MM-DD HH:mm';
export const FORMAT_TIME_SECOND = 'HH:mm:ss';
export const FORMAT_UTC = 'YYYY-MM-DDTHH:mm:ss.SSS[Z]';
export const FORMAT_DAY = 'DD/MM';
export const FORMAT_DATE_TIME_SECOND = 'YYYY-MM-DD HH:mm:ss';
export const OPTIONS_GENDER = [
  { label: 'Nam', value: 0 },
  { label: 'Nữ', value: 1 },
  // { label: 'Khác', value: 2 },
];
export const OPTIONS_GENDER_STRING = [
  { label: 'Nam', value: '0' },
  { label: 'Nữ', value: '1' },
  // { label: 'Khác', value: 2 },
];
export const OPTIONS_LINEMANAGER = [
  { label: 'Nhân viên', value: true },
  { label: 'Quản lý', value: false },
];
export const OPTIONS_LOAN_TYPE = [
  { label: 'Có', value: true },
  { label: 'Không', value: false },
];

export const OPTIONS_LINEMANAGER_EMPLOYEE = [
  { label: 'Quản lý', value: 1 },
  { label: 'Nhân viên', value: 2 },
];

export const OPTIONS_POSITION_ACCOUNT = [
  { label: 'Admin', value: true },
  { label: 'Người dùng', value: false },
];

export const OPTIONS_STATUS = [
  { label: 'Hoạt động', value: 1 },
  { label: 'Nghỉ việc', value: 2 },
];

export const LEVEL_DVBH: { [key: number]: string } = {
  0: 'Tập đoàn',
  1: 'Công ty',
  2: 'Khối/Ban',
  3: 'Phòng',
  4: 'Bộ phận',
  5: 'Nhóm',
  6: 'Tổ/Đội',
};

export const SOURCE_ROLE_DATA_TYPE: SelectProps['options'] = [
  { label: 'Sơ đồ tổ chức', value: 'INTERNAL_ORGCHART' },
  { label: 'Đơn vị HTKD', value: 'EXTERNAL_ORGCHART' },
  { label: 'Dự án', value: 'PROJECT', style: { display: 'none' } },
];

export const REGEX_EMAIL = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

export const OPTIONS_IDENTITIES: SelectProps['options'] = [
  {
    label: 'Chứng minh nhân dân',
    value: 'CMND',
  },
  {
    label: 'Căn cước công dân',
    value: 'CCCD',
  },
  {
    label: 'Hộ chiếu',
    value: 'Hộ chiếu',
  },
];

export const OPTIONS_RELATIONSHIP: SelectProps['options'] = [
  { label: 'Độc thân', value: 'Độc thân' },
  { label: 'Đã kết hôn', value: 'Đã kết hôn' },
  { label: 'Ly thân', value: 'Ly thân' },
  { label: 'Ly hôn', value: 'Ly hôn' },
  { label: 'Góa bụa', value: 'Góa bụa' },
  { label: 'Khác', value: 'Khác' },
];

export const OPTIONS_PROJECT_TYPE = [
  { label: 'Phát triển', value: '01' },
  { label: 'Xây dựng', value: '02' },
  { label: 'Môi giới', value: '03' },
  { label: 'Thương mại', value: '04' },
];
export const OPTIONS_STATUS_PROJECT = [
  { label: 'Đang hoạt động', value: '01' },
  { label: 'Vô hiệu hóa', value: '02' },
];
export const OPTIONS_CONSTRUCTION_PROGRESS_PROJECT = [
  { label: 'Chuẩn bị', value: '01' },
  { label: 'Hoàn tất giấy phép', value: '02' },
  { label: 'Đang xây dựng', value: '03' },
  { label: 'Hoàn thành', value: '04' },
];
export const OPTIONS_GENDER_V2 = [
  { label: 'Nam', value: 'Male' },
  { label: 'Nữ', value: 'Female' },
];

export const OPTIONS_STATUS_FILTER = [
  { label: 'Đã kích hoạt', value: 1, color: '#389E0D' },
  { label: 'Vô hiệu hóa', value: 2, color: '#CF1322' },
];
export const OBJECT_ALL_WEEK = [
  { key: 'monday', label: 'Thứ hai', value: 'monday' },
  { key: 'tuesday', label: 'Thứ ba', value: 'tuesday' },
  { key: 'wednesday', label: 'Thứ tư', value: 'wednesday' },
  { key: 'thursday', label: 'Thứ năm', value: 'thursday' },
  { key: 'friday', label: 'Thứ sáu', value: 'friday' },
  { key: 'saturday', label: 'Thứ bảy', value: 'saturday' },
  { key: 'sunday', label: 'Chủ nhật', value: 'sunday' },
];

export const OPTIONS_TYPE_LEAD = [
  { value: 1, label: 'Phân bổ thủ công' },
  { value: 2, label: 'Phân bổ tự động' },
];
export const OPTIONS_WORKING_TIME = [
  { value: false, label: 'Toàn thời gian' },
  { value: true, label: 'Theo giờ làm việc' },
];

export const OPTIONS_SEND_NOTIFICATIONS = [
  { label: 'Web', value: 'web' },
  { label: 'App', value: 'app' },
  { label: 'Email', value: 'email' },
  { label: 'SMS', value: 'sms' },
];

export const defaultWorkingTimes: WorkingTimeConvert[] = [
  { name: 'Thứ hai', timeRange: [null, null] },
  { name: 'Thứ ba', timeRange: [null, null] },
  { name: 'Thứ tư', timeRange: [null, null] },
  { name: 'Thứ năm', timeRange: [null, null] },
  { name: 'Thứ sáu', timeRange: [null, null] },
  { name: 'Thứ bảy', timeRange: [null, null] },
  { name: 'Chủ nhật', timeRange: [null, null] },
];
// danh sachs 7 ngay trong tuan.
export const daysOfWeek = ['Thứ hai', 'Thứ ba', 'Thứ tư', 'Thứ năm', 'Thứ sáu', 'Thứ bảy', 'Chủ nhật'];

export const OPTIONS_STATUS_KHTN_FILTER = [
  { label: 'Mới', value: 'NORMAL' },
  { label: 'Đang giao dịch', value: 'PENDING_CUST' },
  { label: 'KHCT', value: 'CUST_DONE' },
];

export const OPTIONS_STATUS_SALE_PROGRAM = [
  { label: 'Đang hoạt động', value: '01' },
  { label: 'Ngưng hoạt động', value: '02' },
];

export const OPTIONS_STATUS_IMPORT_CUSTOMERS = [
  { label: 'chờ xử lý', value: 'PENDING', color: '#0958D9' },
  { label: 'Đang Xử Lý', value: 'PROCESSING', color: '#FFD666' },
  { label: 'Thành Công', value: 'SUCCESS', color: '#52C41A' },
  { label: 'Lỗi một phần', value: 'PARTIAL_ERROR', color: '#FAAD14' },
  { label: 'Lỗi toàn phần', value: 'ENTIRE_ERROR', color: '#F5222D' },
];

export const TYPE_DEMAND_CUSTOMERS = [
  { label: 'Khách hàng cá nhân', value: 'DEMAND_CUSTOMER_INVIDIUAL' },
  { label: 'Khách hàng doanh nghiệp', value: 'DEMAND_CUSTOMER_BUSINESS' },
];

export const REGEX_PHONE_VN = /^0\d{8,14}$/;

export const TYPE_LEAD_SOURCE = [
  { label: 'Lead thường', value: 'false' },
  { label: 'Lead hot', value: 'true' },
];
export const IMPORT_HISTORY_STATUS = {
  processing: 'PROCESSING',
  entire_error: 'ENTIRE_ERROR',
  success: 'SUCCESS',
  pending: 'PENDING',
  partial_error: 'PARTIAL_ERROR',
};
export const IMPORT_HISTORY_STATUS_NAME = {
  [IMPORT_HISTORY_STATUS.processing]: 'Đang xử lý',
  [IMPORT_HISTORY_STATUS.entire_error]: 'Thất bại',
  [IMPORT_HISTORY_STATUS.success]: 'Hoàn thành',
  [IMPORT_HISTORY_STATUS.pending]: 'Chờ xử lý',
  [IMPORT_HISTORY_STATUS.partial_error]: 'Thất bại một phần',
};

export const IMPORT_HISTORY_STATUS_LIST = [
  { label: 'Đang xử lý', value: 'PROCESSING' },
  { label: 'Thất bại', value: 'ENTIRE_ERROR' },
  { label: 'Hoàn thành', value: 'SUCCESS' },
  { label: 'Chờ xử lý', value: 'PENDING' },
  { label: 'Thất bại một phần', value: 'PARTIAL_ERROR' },
];

export const _IMPORT_HISTORY_STATUS_COLOR = {
  [IMPORT_HISTORY_STATUS.processing]: '#1677FF',
  [IMPORT_HISTORY_STATUS.entire_error]: '#F5222D',
  [IMPORT_HISTORY_STATUS.success]: '#52C41A',
  [IMPORT_HISTORY_STATUS.pending]: '#FAAD14',
  [IMPORT_HISTORY_STATUS.partial_error]: '#FF4D4F',
};

export const EXPLOIT_STATUS_MAP = {
  manual_deliver: { label: 'Phân bổ thủ công', color: '#FFEC3D', backgroundColor: '#4096ff' },
  assign: { label: 'Đã phân bổ', color: '#1677FF', backgroundColor: '#E6F4FF' },
  processing: { label: 'Đang khảo sát', color: '#FAAD14', backgroundColor: '#FFFB8F' },
  done: { label: 'Khách hàng tiềm năng', color: '#52C41A', backgroundColor: '#B7EB8F' },
  cancel: { label: 'Đã trả về', backgroundColor: '#FFA39E', color: '#FF4D4F' },
};

export const OPTIONS_APPROVAL_STATUS = [
  { label: 'Đang soạn thảo', value: 'NEW' },
  { label: 'Chờ đệ trình', value: 'INIT' },
  { label: 'Đang phê duyệt', value: 'WAITING' },
  { label: 'Đã duyệt', value: 'APPROVED' },
  { label: 'Thu hồi', value: 'RETURNED' },
  { label: 'Trả về', value: 'REJECTED' },
  { label: 'Huỷ', value: 'CANCELLED ' },
];

export enum Status {
  NEW = 'Đang soạn thảo',
  INIT = 'Chờ đệ trình',
  APPROVED = 'Đã duyệt',
  WAITING = 'Đang phê duyệt',
  RETURNED = 'Thu hồi',
  REJECTED = 'Trả về',
  CANCELED = 'Hủy',
}
enum StatusPolicy {
  NEW = 'Khởi tạo',
  INIT = 'Chờ duyệt',
  APPROVED_ACTIVE = 'Kích hoạt',
  APPROVED_INACTIVE = 'Vô hiệu',
  WAITING = 'Chờ duyệt',
  RETURNED = 'Khởi tạo',
  REJECTED = 'Khởi tạo',
  CANCELLED = 'Khởi tạo',
}
interface StatusObject {
  key: string;
  value: string;
  label: string;
}
export function getStatusObject(status: string): StatusObject {
  switch (status) {
    case 'INIT':
      return { key: 'INIT', value: '#389E0D', label: Status.INIT };
    case 'APPROVED':
      return { key: 'APPROVED', value: '#1677FF', label: Status.APPROVED };
    case 'WAITING':
      return { key: 'WAITING', value: '#FAAD14', label: Status.WAITING };
    case 'RETURNED':
      return { key: 'RETURNED', value: '#FFC53D', label: Status.RETURNED };
    case 'REJECTED':
      return { key: 'REJECTED', value: '#FF4D4F', label: Status.REJECTED };
    case 'CANCELED':
      return { key: 'CANCELED', value: '#820014', label: Status.CANCELED };
    case 'NEW':
      return { key: 'NEW', value: '#D48806', label: Status.NEW };
    default:
      return { key: 'UNKNOWN', value: '#D48806', label: 'Không xác định' };
  }
}
export function getPolicyStatus(status: string): StatusObject {
  switch (status) {
    case 'INIT':
      return { key: 'INIT', value: '#FA8C16', label: StatusPolicy.INIT };
    case 'APPROVED_ACTIVE':
      return { key: 'APPROVED', value: '#52C41A', label: StatusPolicy.APPROVED_ACTIVE };
    case 'APPROVED_INACTIVE':
      return { key: 'APPROVED', value: '#FF4D4F', label: StatusPolicy.APPROVED_INACTIVE };
    case 'WAITING':
      return { key: 'WAITING', value: '#FA8C16', label: StatusPolicy.WAITING };
    case 'RETURNED':
      return { key: 'RETURNED', value: '#FA8C16', label: StatusPolicy.RETURNED };
    case 'REJECTED':
      return { key: 'REJECTED', value: '#FA8C16', label: StatusPolicy.REJECTED };
    case 'CANCELLED':
      return { key: 'CANCELLED', value: '#FA8C16', label: StatusPolicy.CANCELLED };
    case 'NEW':
      return { key: 'NEW', value: '#FA8C16', label: StatusPolicy.NEW };
    default:
      return { key: 'UNKNOWN', value: '#FA8C16', label: 'Không xác định' };
  }
}

export const EAPP_STATUS = {
  INIT: 'INIT',
  DRAFT: 'DRAFT',
  OPENED: 'OPENED',
  PROCESSING: 'PROCESSING',
  COMPLETED: 'COMPLETED',
  CLOSE: 'CLOSE',
  RECALLING: 'RECALLING',
  RECALLED: 'RECALLED',
  ADDITIONAL_REQUEST: 'ADDITIONAL_REQUEST',
  DELETED_BY_RU: 'DELETED_BY_RU',
  CANCEL: 'CANCEL',
  DELETE_DRAFT_TICKET: 'DELETE_DRAFT_TICKET',
};

export const EAPP_STATUS_NAME = {
  [EAPP_STATUS.INIT]: 'Chờ đệ trình',
  [EAPP_STATUS.DRAFT]: 'Chờ đệ trình',
  [EAPP_STATUS.OPENED]: 'Đang phê duyệt',
  [EAPP_STATUS.PROCESSING]: 'Đang phê duyệt',
  [EAPP_STATUS.COMPLETED]: 'Đã duyệt',
  [EAPP_STATUS.CLOSE]: 'Đã duyệt',
  [EAPP_STATUS.RECALLING]: 'Đang phê duyệt',
  [EAPP_STATUS.RECALLED]: 'Thu hồi',
  [EAPP_STATUS.ADDITIONAL_REQUEST]: 'Đang phê duyệt',
  [EAPP_STATUS.DELETED_BY_RU]: 'Trả về',
  [EAPP_STATUS.CANCEL]: 'Huỷ',
  [EAPP_STATUS.DELETE_DRAFT_TICKET]: 'Đang soạn thảo',
};

export const EAPP_STATUS_COLOR = {
  [EAPP_STATUS.INIT]: '#FA8C16',
  [EAPP_STATUS.DRAFT]: '#FA8C16',
  [EAPP_STATUS.OPENED]: '#FAAD14',
  [EAPP_STATUS.PROCESSING]: '#FAAD14',
  [EAPP_STATUS.COMPLETED]: '#1677FF',
  [EAPP_STATUS.CLOSE]: '#1677FF',
  [EAPP_STATUS.RECALLING]: '#FA8C16',
  [EAPP_STATUS.RECALLED]: '#FA8C16',
  [EAPP_STATUS.ADDITIONAL_REQUEST]: '#FA8C16',
  [EAPP_STATUS.DELETED_BY_RU]: '#FF4D4F',
  [EAPP_STATUS.CANCEL]: '#820014',
  [EAPP_STATUS.DELETE_DRAFT_TICKET]: '#D48806',
};

export const OPTIONS_POLICY_TYPE = [
  { label: 'Phí môi giới của chủ đầu tư', value: 'Phí môi giới của chủ đầu tư' },
  { label: 'Nhân viên kinh doanh', value: 'Nhân viên kinh doanh' },
  { label: 'Sàn liên kết', value: 'Sàn liên kết' },
];

export const OPTIONS_STATUS_TRANSACTIONS = [
  { label: 'Khởi tạo', value: 'init' },
  { label: 'Chờ duyệt', value: 'waiting' },
  { label: 'Chờ KT thu tiền', value: 'accountant_waiting' },
  { label: 'Đã duyệt', value: 'approved' },
  { label: 'Đã thanh lý', value: 'liquidated' },
  { label: 'Chờ KT xác nhận thu và hoàn tiền', value: 'accountant_waiting_confirm_refund' },
  { label: 'Đã từ chối', value: 'rejected' },
  { label: 'Đã hủy', value: 'cancelled' },
];

export enum InputNumberType {
  INTEGER = 'integer',
  DECIMAL = 'decimal',
}

export const OPTIONS_EXPLOIT_LEAD = [
  { label: 'Lead đã phân bổ', value: 'assign' },
  { label: 'Lead đang khảo sát', value: 'processing' },
  { label: 'Lead hoàn thành', value: 'done' },
  { label: 'Lead trả về', value: 'cancel' },
];

export const OPTIONS_TYPE_COMMISSION_POLICY = [
  { label: 'Cá nhân', value: 'PERSONAL' },
  { label: 'Quản lý', value: 'MANAGE' },
];

export const OPTIONS_STATUS_KPI = [
  { label: 'Đã kích hoạt', value: 1 },
  { label: 'Vô hiệu hóa', value: 2 },
];

export enum STATUS_ADJUSTMENT_VERSION {
  NEW = 'NEW',
  WAITING = 'WAITING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  ANNOUNCED = 'ANNOUNCED',
}

export const OPTIONS_STATUS_ADJUSTMENT_VERSION = [
  { name: 'Mới tạo', value: STATUS_ADJUSTMENT_VERSION.NEW, color: '#FA8C16' },
  { name: 'Đang phê duyệt', value: STATUS_ADJUSTMENT_VERSION.WAITING, color: '#1677FF' },
  { name: 'Đã duyệt', value: STATUS_ADJUSTMENT_VERSION.APPROVED, color: '#52C41A' },
  { name: 'từ chối', value: STATUS_ADJUSTMENT_VERSION.REJECTED, color: '#FF4D4F' },
  { name: 'Công bố', value: STATUS_ADJUSTMENT_VERSION.ANNOUNCED, color: '#2f54eb' },
];

export const OPTIONS_STATUS_IMPORT_COMMISSIONS = [
  { label: 'Thành Công', value: 'SUCCESS', color: '#52C41A' },
  { label: 'Lỗi toàn phần', value: 'ENTIRE_ERROR', color: '#F5222D' },
];

export const OPTIONS_PERIOD = [
  { label: 'Kỳ 1: 01/01 → 31/01', value: '01/01 → 31/01' },
  { label: 'Kỳ 2: 01/02 → 31/02', value: '01/02 → 31/02' },
  { label: 'Kỳ 3: 01/03 → 31/03', value: '01/03 → 31/03' },
  { label: 'Kỳ 4: 01/04 → 30/04', value: '01/04 → 30/04' },
  { label: 'Kỳ 5: 01/05 → 31/05', value: '01/05 → 31/05' },
  { label: 'Kỳ 6: 01/06 → 30/06', value: '01/06 → 30/06' },
  { label: 'Kỳ 7: 01/07 → 31/07', value: '01/07 → 31/07' },
  { label: 'Kỳ 8: 01/08 → 31/08', value: '01/08 → 31/08' },
  { label: 'Kỳ 9: 01/09 → 30/09', value: '01/09 → 30/09' },
  { label: 'Kỳ 10: 01/10 → 31/10', value: '01/10 → 31/10' },
  { label: 'Kỳ 11: 01/11 → 30/11', value: '01/11 → 30/11' },
  { label: 'Kỳ 12: 01/12 → 31/12', value: '01/12 → 31/12' },
];

export const ALLOWED_EXTENSIONS = ['png', 'jpg', 'jpeg', 'xls', 'xlsx', 'doc', 'docx', 'pdf', 'ppt', 'pptx'];

export const dataStatusSellProgram = [
  { label: 'Đang hoạt động', value: '01' },
  { label: 'Ngưng hoạt động', value: '02' },
];

export const TRANSACTION_STATUS = {
  initial: 'INITIAL',
  received: 'RECEIVED',
  transfered: 'TRANSFERED',
  processing: 'PROCESSING',
  waiting_transfer: 'WAITING_TRANSFER',
  refunded: 'REFUNDED',
  canceled: 'CANCELED',
};

export const TRANSACTION_STATUS_NAME = {
  [TRANSACTION_STATUS.initial]: 'INITIAL',
  [TRANSACTION_STATUS.received]: 'RECEIVED',
  [TRANSACTION_STATUS.transfered]: 'TRANSFERED',
  [TRANSACTION_STATUS.processing]: 'PROCESSING',
  [TRANSACTION_STATUS.waiting_transfer]: 'WAITING_TRANSFER',
  [TRANSACTION_STATUS.refunded]: 'REFUNDED',
  [TRANSACTION_STATUS.canceled]: 'CANCELED',
};

export const TRANSACTION_STATUS_COLOR = {
  [TRANSACTION_STATUS.initial]: 'Khởi tạo',
  [TRANSACTION_STATUS.received]: 'Chờ duyệt',
  [TRANSACTION_STATUS.transfered]: 'Kích hoạt/Vô hiệu hóa',
  [TRANSACTION_STATUS.processing]: 'Khởi tạo',
  [TRANSACTION_STATUS.waiting_transfer]: 'Chờ duyệt',
  [TRANSACTION_STATUS.refunded]: 'Kích hoạt/Vô hiệu hóa',
  [TRANSACTION_STATUS.canceled]: 'Kích hoạt/Vô hiệu hóa',
};
export const PRIMARY_STATUS_LABELS: { [key: string]: { label: string; color: string } } = {
  COMING: { label: 'Đang mở bán', color: '#FAAD14' },
  PROCESSING: { label: 'ĐVBH đã đăng ký GD', color: '#FAAD14' },
  CONFIRM: { label: 'ĐVBH đã xác nhận', color: '#9254DE' },
  LOCK_CONFIRM: { label: 'Đang kiểm tra hồ sơ', color: '#FAAD14' },
  SUCCESS: { label: 'Chuyển cọc thành công', color: '#52C41A' },
  LOCK: { label: 'Khóa đầu tư', color: '#FF4D4F' },
  CLOSE: { label: 'Chưa mở bán', color: '#1890FF' },
  MCONFIRM: { label: 'KH xem - Đang kiểm tra hồ sơ', color: '#9254DE' },
  MSUCCESS: { label: 'KH xem - Giao dịch thành công', color: '#52C41A' },
  LOCK_CONFIRM_LOCK: { label: 'Xác nhận cọc, chờ bổ sung tiền và hồ sơ', color: '#FAAD14' },
  UNSUCCESS: { label: 'Giao dịch thành công', color: '#52C41A' },
  CANCEL: { label: 'Thanh lý', color: '#FF4D4F' },
  MOVED: { label: 'Đã chuyển CTBH', color: '#722ED1' },
};

export const PRIMARY_STATUS = [
  { label: 'Khóa', value: 'LOCK', className: 'status-lock' },
  { label: 'Đóng', value: 'CLOSE', className: 'status-close' },
  { label: 'Sắp mở', value: 'COMING', className: 'status-coming' },
  { label: 'Đang xử lý', value: 'PROCESSING', className: 'status-processing' },
  { label: 'Xác nhận', value: 'CONFIRM', className: 'status-confirm' },
  { label: 'Xác nhận chờ', value: 'MCONFIRM', className: 'status-mconfirm' },
  { label: 'Thành công', value: 'SUCCESS', className: 'status-success' },
  { label: 'Thành công chờ', value: 'MSUCCESS', className: 'status-msuccess' },
  { label: 'Khóa xác nhận', value: 'LOCK_CONFIRM', className: 'status-lock-confirm' },
  { label: 'Khóa xác nhận khóa', value: 'LOCK_CONFIRM_LOCK', className: 'status-lock-confirm-lock' },
  { label: 'Không thành công', value: 'UNSUCCESS', className: 'status-unsuccess' },
  { label: 'Hủy', value: 'CANCEL', className: 'status-cancel' },
  { label: 'Đã chuyển', value: 'MOVED', className: 'status-moved' },
];

export function getPrimaryStatusLabel(status: string): { label: string; color: string } {
  return PRIMARY_STATUS_LABELS[status] || { label: 'Không xác định', color: '#8C8C8C' };
}

export const ALLOWED_PROPOSAL_EXTENSIONS = ['.doc', '.docx', '.pdf', '.xls', '.xlsx'];

export const ALLOWED_ATTACHMENT_EXTENSIONS = [
  '.png',
  '.jpg',
  '.jpeg',
  '.mp4',
  '.avi',
  '.mov',
  '.wmv',
  '.xls',
  '.xlsx',
  '.doc',
  '.docx',
  '.pdf',
  '.ppt',
  '.pptx',
  '.jfif',
  '.rar',
  '.zip',
  '.msg',
  '.txt',
];
export const TypeProposal = [
  // { label: 'Hợp đồng dịch vụ', value: 'primary_contract' },
  { label: 'Yêu cầu đặt chỗ', value: 'YCDCH' },
  { label: 'Yêu cầu đặt cọc', value: 'YCDC' },
];
export const STATUS = [
  { label: 'Chờ duyệt', value: 'WAITING_TRANSFER' },
  { label: 'Đã duyệt', value: 'TRANSFERED' },
  { label: 'Bị từ chối', value: 'PROCESSING' },
  { label: 'Đã hủy', value: 'CANCELED ' },
  { label: 'Đã hoàn', value: 'REFUNDED' },
];
export const STATUS_TICKET = [
  { label: 'Khóa đầu tư', value: 'LOCK' },
  { label: 'Chưa mở bán', value: 'CLOSE' },
  { label: 'Đang mở bán', value: 'COMING' },
  { label: 'ĐVBH đã đăng ký GD', value: 'PROCESSING' },
  { label: 'ĐVBH đã xác nhận', value: 'CONFIRM' },
  { label: 'Đang kiểm tra hồ sơ', value: 'LOCK_CONFIRM' },
  { label: 'Giao dịch thành công', value: 'SUCCESS' },
  { label: 'Giao dịch không thành công', value: 'UNSUCCESS' },
  { label: 'Thanh lý', value: 'CANCEL' },
  { label: 'Đã chuyển CTBH', value: 'MOVED' },
  { label: 'KH - Đang kiểm tra hồ sơ', value: 'MCONFIRM' },
  { label: 'KH - Đã kiểm tra hồ sơ', value: 'MSUCCESS' },
  { label: 'Đổi tên/Chuyển nhượng', value: 'TRANSFER' },
  { label: 'Hợp đồng', value: 'DEPOSIT' },
];
export const BOOKING_TICKET_STATUS = {
  close: 'CLOSE', //Chờ ĐVBH xác nhận,
  admin_approved_ticket: 'ADMIN_APPROVED_TICKET', //ĐVBH đã xác nhận YCĐCH,
  deposit_approved: 'DEPOSIT_APPROVED', //KT đã xác nhận ĐNTT,
  cs_approved_ticket: 'CS_APPROVED_TICKET', //ĐVBH đã xác nhận,
  booking_approved: 'BOOKING_APPROVED', //Giữ chỗ thành công,
  priority_updated: 'PRIORITY_UPDATED', //Đã ráp ưu tiên,
  coming: 'COMING', //Chờ KH xác nhận,
  processing: 'PROCESSING', // KH đã xác nhận,
  processing_lock: 'PROCESSING_LOCK', //Chờ KH xác nhận,
  pos_confirm: 'POS_CONFIRM', //ĐVBH đã xác nhận GD,
  lock_confirm: 'LOCK_CONFIRM', //Chuyển cọc thành công, chờ bổ sung hồ sơ,
  lock_confirm_lock: 'LOCK_CONFIRM_LOCK', //Xác nhận cọc, chờ KT xác nhận tiền,
  pos_confirm_lock: 'POS_CONFIRM_LOCK', //ĐVBH xác nhận, chờ KT xác nhận tiền,
  success: 'SUCCESS', //Chuyển cọc thành công,
  processing_unpaid: 'PROCESSING_UNPAID', //Cọc mới, chưa thanh toán,
  pos_confirm_unpaid: 'POS_CONFIRM_UNPAID', // ĐVBH xác nhận, chờ SA / ĐVBH xác nhận,
  sa_confirm_unpaid: 'SA_CONFIRM_UNPAID', //SA / ĐVBH xác nhận, chờ KT xác nhận tiền,
  unsuccess: 'UNSUCCESS', //GD không thành công,
  cancel_request: 'CANCEL_REQUESTED', //Đề nghị hủy giữ chỗ,
  admin_approved_cancel_requested: 'ADMIN_APPROVED_CANCEL_REQUESTED', //ĐVBH đã duyệt hủy chỗ,
  ticket_cancelled: 'TICKET_CANCELLED', //Hủy chỗ thành công,
  cs_approved_cancel_requested: 'CS_APPROVED_CANCEL_REQUESTED', //ĐVBH đã xác nhận hủy chỗ,
  admin_rejected_ticket: 'ADMIN_REJECTED_TICKET', //ĐVBH đã từ chối,
  deposit_reject: 'DEPOSIT_REJECT', //KT đã trả về ĐNTT,
  cs_rejected_ticket: 'CS_REJECTED_TICKET', // ĐVBH đã từ chối,
  pos_reject: 'POS_REJECT', //ĐVBH không xác nhận cọc,
  pos_reject_unpaid: 'POS_REJECT_UNPAID', //ĐVBH từ chối,
  sa_reject_unpaid: 'SA_REJECT_UNPAID', //SA / ĐVBH từ chối,
  cs_reject_escrow: 'CS_REJECT_ESCROW', //ĐVKH từ chối cọc,
  refunded: 'REFUNDED', //Đã hoàn tiền,
  waiting_otp: 'WAITING_OTP', //Chờ xác nhận,
  otp_waiting_sms_lock: 'OTP_WAITING_SMS_LOCK', //Chờ xác nhận SMS GD2,
  waiting_sms: 'WAITING_SMS', //Chờ xác nhận SMS,
  admin_cancel_requested: 'ADMIN_CANCEL_REQUESTED', //ĐVBH từ chối duyệt hủy chỗ,
  cs_cancel_requested: 'CS_CANCEL_REQUESTED', //ĐVKH từ chối duyệt hủy chỗ,
  liquidated: 'LIQUIDATED', //Đã thanh lý,
  waiting_transfer: 'WAITING_TRANSFER', //Chờ duyệt
};

export const BOOKING_TICKET_STATUS_NAME = {
  [BOOKING_TICKET_STATUS.close]: 'Chờ ĐVBH xác nhận',
  [BOOKING_TICKET_STATUS.admin_approved_ticket]: 'ĐVBH đã xác nhận YCĐCH',
  [BOOKING_TICKET_STATUS.deposit_approved]: 'KT đã xác nhận ĐNTT',
  [BOOKING_TICKET_STATUS.cs_approved_ticket]: 'Dịch vụ KH đã xác nhận',
  [BOOKING_TICKET_STATUS.booking_approved]: 'Giữ chỗ thành công',
  [BOOKING_TICKET_STATUS.priority_updated]: 'Đã ráp ưu tiên',
  [BOOKING_TICKET_STATUS.coming]: 'Chờ KH xác nhận',
  [BOOKING_TICKET_STATUS.processing]: 'KH đã xác nhận',
  [BOOKING_TICKET_STATUS.processing_lock]: 'Chờ KH xác nhận',
  [BOOKING_TICKET_STATUS.pos_confirm]: 'ĐVBH đã xác nhận GD',
  [BOOKING_TICKET_STATUS.lock_confirm]: 'Chuyển cọc thành công, chờ bổ sung hồ sơ',
  [BOOKING_TICKET_STATUS.lock_confirm_lock]: 'Xác nhận cọc, chờ KT xác nhận tiền',
  [BOOKING_TICKET_STATUS.pos_confirm_lock]: 'ĐVBH xác nhận, chờ KT xác nhận tiền',
  [BOOKING_TICKET_STATUS.success]: 'Chuyển cọc thành công',
  [BOOKING_TICKET_STATUS.processing_unpaid]: 'Cọc mới, chưa thanh toán',
  [BOOKING_TICKET_STATUS.pos_confirm_unpaid]: 'ĐVBH xác nhận, chờ SA / ĐVBH xác nhận',
  [BOOKING_TICKET_STATUS.sa_confirm_unpaid]: 'SA / ĐVBH xác nhận, chờ KT xác nhận tiền',
  [BOOKING_TICKET_STATUS.unsuccess]: 'GD không thành công',
  [BOOKING_TICKET_STATUS.cancel_request]: 'Đề nghị hủy giữ chỗ',
  [BOOKING_TICKET_STATUS.admin_approved_cancel_requested]: 'ĐVBH đã duyệt hủy chỗ',
  [BOOKING_TICKET_STATUS.ticket_cancelled]: 'Hủy chỗ thành công',
  [BOOKING_TICKET_STATUS.cs_approved_cancel_requested]: 'DVKH đã xác nhận hủy chỗ',
  [BOOKING_TICKET_STATUS.admin_rejected_ticket]: 'ĐVBH đã từ chối',
  [BOOKING_TICKET_STATUS.deposit_reject]: 'KT đã trả về ĐNTT',
  [BOOKING_TICKET_STATUS.cs_rejected_ticket]: 'ĐVBH đã từ chối',
  [BOOKING_TICKET_STATUS.pos_reject]: 'ĐVBH không xác nhận cọc',
  [BOOKING_TICKET_STATUS.pos_reject_unpaid]: 'ĐVBH từ chối',
  [BOOKING_TICKET_STATUS.sa_reject_unpaid]: 'SA / ĐVBH từ chối',
  [BOOKING_TICKET_STATUS.cs_reject_escrow]: 'ĐVKH từ chối cọc',
  [BOOKING_TICKET_STATUS.refunded]: 'Đã hoàn tiền',
  [BOOKING_TICKET_STATUS.waiting_otp]: 'Chờ xác nhận',
  [BOOKING_TICKET_STATUS.otp_waiting_sms_lock]: 'Chờ xác nhận SMS GD2',
  [BOOKING_TICKET_STATUS.waiting_sms]: 'Chờ xác nhận SMS',
  [BOOKING_TICKET_STATUS.admin_cancel_requested]: 'ĐVBH từ chối duyệt hủy chỗ',
  [BOOKING_TICKET_STATUS.cs_cancel_requested]: 'ĐVKH từ chối duyệt hủy chỗ',
  [BOOKING_TICKET_STATUS.liquidated]: 'Đã thanh lý',
  [BOOKING_TICKET_STATUS.waiting_transfer]: 'Chờ duyệt',
};

export const BOOKING_TICKET_STATUS_COLOR = {
  [BOOKING_TICKET_STATUS.close]: '',
  [BOOKING_TICKET_STATUS.admin_approved_ticket]: '',
  [BOOKING_TICKET_STATUS.deposit_approved]: '',
  [BOOKING_TICKET_STATUS.cs_approved_ticket]: '',
  [BOOKING_TICKET_STATUS.booking_approved]: '',
  [BOOKING_TICKET_STATUS.priority_updated]: '',
  [BOOKING_TICKET_STATUS.coming]: '',
  [BOOKING_TICKET_STATUS.processing]: '',
  [BOOKING_TICKET_STATUS.processing_lock]: '',
  [BOOKING_TICKET_STATUS.pos_confirm]: '',
  [BOOKING_TICKET_STATUS.lock_confirm]: '',
  [BOOKING_TICKET_STATUS.lock_confirm_lock]: '',
  [BOOKING_TICKET_STATUS.pos_confirm_lock]: '',
  [BOOKING_TICKET_STATUS.success]: '#389E0D',
  [BOOKING_TICKET_STATUS.processing_unpaid]: '',
  [BOOKING_TICKET_STATUS.pos_confirm_unpaid]: '',
  [BOOKING_TICKET_STATUS.sa_confirm_unpaid]: '',
  [BOOKING_TICKET_STATUS.unsuccess]: ' #FF4D4F',
  [BOOKING_TICKET_STATUS.cancel_request]: '',
  [BOOKING_TICKET_STATUS.admin_approved_cancel_requested]: '',
  [BOOKING_TICKET_STATUS.ticket_cancelled]: '',
  [BOOKING_TICKET_STATUS.cs_approved_cancel_requested]: '',
  [BOOKING_TICKET_STATUS.admin_rejected_ticket]: '',
  [BOOKING_TICKET_STATUS.deposit_reject]: '',
  [BOOKING_TICKET_STATUS.cs_rejected_ticket]: '',
  [BOOKING_TICKET_STATUS.pos_reject]: '',
  [BOOKING_TICKET_STATUS.pos_reject_unpaid]: '',
  [BOOKING_TICKET_STATUS.sa_reject_unpaid]: '',
  [BOOKING_TICKET_STATUS.cs_reject_escrow]: '',
  [BOOKING_TICKET_STATUS.refunded]: '',
  [BOOKING_TICKET_STATUS.waiting_otp]: '',
  [BOOKING_TICKET_STATUS.otp_waiting_sms_lock]: '',
  [BOOKING_TICKET_STATUS.waiting_sms]: '',
  [BOOKING_TICKET_STATUS.admin_cancel_requested]: '',
  [BOOKING_TICKET_STATUS.cs_cancel_requested]: '',
  [BOOKING_TICKET_STATUS.liquidated]: '',
  [BOOKING_TICKET_STATUS.waiting_transfer]: '',
};

export const OFFER_STATUS = {
  initial: 'INITIAL',
  waiting_transfer: 'WAITING_TRANSFER',
  transfered: 'TRANSFERED',
  processing: 'PROCESSING',
  refunded: 'REFUNDED',
  canceled: 'CANCELED',
};

export const OFFER_STATUS_NAME = {
  [OFFER_STATUS.initial]: 'Khởi tạo',
  [OFFER_STATUS.waiting_transfer]: 'Chờ duyệt',
  [OFFER_STATUS.transfered]: 'Đã duyệt',
  [OFFER_STATUS.processing]: 'Bị từ chối',
  [OFFER_STATUS.refunded]: 'Đã hoàn',
  [OFFER_STATUS.canceled]: 'Đã hủy',
};

export const OFFER_STATUS_COLOR = {
  [OFFER_STATUS.initial]: '',
  [OFFER_STATUS.waiting_transfer]: '#FAAD14',
  [OFFER_STATUS.transfered]: '#52C41A',
  [OFFER_STATUS.processing]: '#1677FF',
  [OFFER_STATUS.refunded]: '#13C2C2',
  [OFFER_STATUS.canceled]: '#F5222D',
};

export const OPTIONS_TYPE_E_VOUCHER = [
  { label: 'Giảm giá', value: 'discount' },
  { label: 'Hiện vật', value: 'artifacts' },
];

export const STATUS_LABELS: { [key: string]: string } = {
  CANCELED: 'Đã hủy',
  PROCESSING: 'Bị từ chối',
  TRANSFERED: 'Đã duyệt',
  WAITING_TRANSFER: 'Chờ duyệt',
  REFUNDED: 'Đã hoàn',
};

export const STATUS_COLORS: { [key: string]: string } = {
  CANCELED: 'red',
  PROCESSING: 'orange',
  TRANSFERED: 'green',
  WAITING_TRANSFER: '#FF9500',
  REFUNDED: '#FF4D4F',
};
export const OPTIONS_HANDOVER = [
  { value: 'category', label: 'Hạng mục' },
  { value: 'index', label: 'Chỉ số' },
];

export const PAYMENT_METHOD = {
  transfer: 'TRANSFER',
  cash: 'CASH',
  dvbh: 'DVBH',
  investor: 'INVESTOR',
  pos: 'POS',
};

export const PAYMENT_METHOD_NAME = {
  [PAYMENT_METHOD.transfer]: 'Chuyển khoản',
  [PAYMENT_METHOD.cash]: 'Tiền mặt',
  [PAYMENT_METHOD.dvbh]: 'Cà thẻ',
  [PAYMENT_METHOD.investor]: 'Đã thu CĐT',
  [PAYMENT_METHOD.pos]: 'Đã thu ĐVBH',
};

export const PAYMENT_METHOD_COLOR = {
  [PAYMENT_METHOD.transfer]: 'Chuyển khoản',
  [PAYMENT_METHOD.cash]: 'Tiền mặt',
  [PAYMENT_METHOD.dvbh]: 'Cà thẻ',
  [PAYMENT_METHOD.investor]: 'Đã thu CĐT',
  [PAYMENT_METHOD.pos]: 'Đã thu ĐVBH',
};

export const DAY_ORDER_MAP = {
  monday: 1,
  tuesday: 2,
  wednesday: 3,
  thursday: 4,
  friday: 5,
  saturday: 6,
  sunday: 7,
};

export const MAX_LENGTH = 1000; // Maximum length of content

export const TRANSACTION_STATUS_COLLUMN = {
  COMING: 'COMING',
  PROCESSING: 'PROCESSING',
  CONFIRM: 'CONFIRM',
  LOCK_COMFIRM: 'LOCK_CONFIRM',
  SUCCESS: 'SUCCESS',
};

export const TRANSACTION_STATUS_COLORS_COLLUMN = {
  [TRANSACTION_STATUS_COLLUMN.COMING]: '#FAAD14',
  [TRANSACTION_STATUS_COLLUMN.PROCESSING]: '#FAAD14',
  [TRANSACTION_STATUS_COLLUMN.CONFIRM]: '#9254DE',
  [TRANSACTION_STATUS_COLLUMN.LOCK_COMFIRM]: '#FAAD14',
  [TRANSACTION_STATUS_COLLUMN.SUCCESS]: '#52c41a',
};

export const TRANSACTION_STATUS_LABELS_COLLUMN = {
  [TRANSACTION_STATUS_COLLUMN.COMING]: 'Đang mở bán',
  [TRANSACTION_STATUS_COLLUMN.PROCESSING]: 'ĐVBH đã đăng ký GD',
  [TRANSACTION_STATUS_COLLUMN.CONFIRM]: 'ĐVBH đã xác nhận',
  [TRANSACTION_STATUS_COLLUMN.LOCK_COMFIRM]: 'Đang kiểm tra hồ sơ',
  [TRANSACTION_STATUS_COLLUMN.SUCCESS]: 'Chuyển cọc thành công',
};

export const getTransactionStatusInfo = (status: string) => {
  return {
    label: TRANSACTION_STATUS_LABELS_COLLUMN[status] || 'Chưa xác định',
    color: TRANSACTION_STATUS_COLORS_COLLUMN[status] || '#000000',
  };
};
export const OPTIONS_STATUS_COMMON = [
  { label: 'Đã kích hoạt', value: 1 },
  { label: 'Vô hiệu hóa', value: 2 },
];
export const OPTIONS_EVENT_TYPE = [
  { label: 'Online', value: 'ONLINE' },
  { label: 'Offline', value: 'OFFLINE' },
];

export const OPTIONS_SOURCE_LIVE = [
  { label: 'Youtube', value: 'youtube' },
  { label: 'Facebook', value: 'facebook' },
  { label: 'Wowza', value: 'wowza' },
  { label: 'Video', value: 'video' },
];

export const OPTIONS_ATTEND_ACCORDINGLY = [
  { value: 'orgChart', label: 'Sàn' },
  { value: 'employee', label: 'Cá nhân/NVKD' },
];

export const OPTIONS_STATUS_PRIZES = [
  { label: 'Chờ ĐVBH xác nhận', value: 'COMING' },
  { label: 'ĐVBH đã xác nhận GD', value: 'POS_CONFIRM' },
  { label: 'Chuyển cọc thành công, chờ bổ sung hồ sơ', value: 'LOCK_CONFIRM' },
  { label: 'GD không thành công', value: 'UNSUCCESS' },
  { label: 'Chuyển cọc thành công', value: 'SUCCESS' },
  { label: 'KH - ĐVBH đã xác nhận', value: 'MPOS_CONFIRM' },
  { label: 'KH - Giao dịch thành công', value: 'MSUCCESS' },
  { label: 'KH đã xác nhận', value: 'PROCESSING' },
];

export const PERIOD = [
  { key: 'kỳ 1', label: 'Kỳ 1', value: 'Kỳ 1' },
  { key: 'kỳ 2', label: 'Kỳ 2', value: 'Kỳ 2' },
  { key: 'kỳ 3', label: 'Kỳ 3', value: 'Kỳ 3' },
  { key: 'kỳ 4', label: 'Kỳ 4', value: 'Kỳ 4' },
  { key: 'kỳ 5', label: 'Kỳ 5', value: 'Kỳ 5' },
  { key: 'kỳ 6', label: 'Kỳ 6', value: 'Kỳ 6' },
  { key: 'kỳ 7', label: 'Kỳ 7', value: 'Kỳ 7' },
  { key: 'kỳ 8', label: 'Kỳ 8', value: 'Kỳ 8' },
  { key: 'kỳ 9', label: 'Kỳ 9', value: 'Kỳ 9' },
  { key: 'kỳ 10', label: 'Kỳ 10', value: 'Kỳ 10' },
  { key: 'kỳ 11', label: 'Kỳ 11', value: 'Kỳ 11' },
  { key: 'kỳ 12', label: 'Kỳ 12', value: 'Kỳ 12' },
];
export const TXN_STATUS: { [key: string]: string } = {
  CLOSE: 'Chờ ĐVBH xác nhận',
  ADMIN_APPROVED_TICKET: 'ĐVBH đã xác nhận YCĐCH',
  DEPOSIT_APPROVED: 'KT đã xác nhận ĐNTT',
  CS_APPROVED_TICKET: 'ĐVBH đã xác nhận',
  BOOKING_APPROVED: 'Giữ chỗ thành công',
  PRIORITY_UPDATE: 'Đã ráp ưu tiên',
  COMING: 'Chờ KH xác nhận',
  PROCESSING: 'KH đã xác nhận',
  PROCESSING_LOCK: 'Chờ KH xác nhận GD2',
  POS_CONFIRM: 'ĐVBH đã xác nhận GD',
  LOCK_CONFIRM: 'Chuyển cọc thành công, chờ bổ sung hồ sơ',
  LOCK_CONFIRM_LOCK: 'Xác nhận cọc, chờ KT xác nhận tiền',
  POS_CONFIRM_LOCK: 'ĐVBH đã xác nhận, chờ KT xác nhận tiền',
  SUCCESS: 'Chuyển cọc thành công',
  PROCESSING_UNPAID: 'Cọc mới, chưa thanh toán',
  POS_CONFIRM_UNPAID: 'ĐVBH xác nhận, chờ SA / ĐVBH xác nhận',
  SA_CONFIRM_UNPAID: 'SA / ĐVBH xác nhận, chờ KT xác nhận tiền',
  UNSUCCESS: 'GD không thành công',
  CANCEL_REQUEST: 'Đề nghị hủy giữ chỗ',
  ADMIN_APPROVED_CANCEL_REQUESTED: 'ĐVBH đã duyệt hủy chỗ',
  ADMIN_CANCEL_REQUESTED: 'ĐVBH từ chối duyệt hủy chỗ',
  TICKET_CANCELLED: 'Hủy chỗ thành công',
  CS_APPROVED_CANCEL_REQUESTED: 'DVKH đã xác nhận hủy chỗ',
  CS_CANCEL_REQUESTED: 'ĐVKH từ chối duyệt hủy chỗ',
  ADMIN_REJECTED_TICKET: 'ĐVBH đã từ chối',
  DEPOSIT_REJECT: 'KT đã trả về ĐNTT',
  CS_REJECTED_TICKET: 'ĐVBH đã từ chối',
  POS_REJECT: 'ĐVBH không xác nhận cọc',
  POS_REJECT_UNPAID: 'ĐVBH từ chối',
  SA_REJECT_UNPAID: 'SA / ĐVBH từ chối',
  CS_REJECT_ESCROW: 'ĐVKH từ chối cọc',
  REFUNDED: 'Đã hoàn tiền',
  WAITING_OTP: 'Chờ xác nhận',
  OTP_WAITING_SMS_LOCK: 'Chờ xác nhận SMS GD2',
  WAITING_SMS: 'Chờ xác nhận SMS',
  LIQUIDATED: 'Đã thanh lý',
  MCONFIRM: 'KH xem - Đang kiểm tra hồ sơ',
  MSUCCESS: 'KH xem - Giao dịch thành công',
  CANCEL_REQUESTED: 'Đề nghị hủy giữ chỗ',
};

export const OPTIONS_HISTORY_EVOUCHER_STATUS = [
  { label: 'Xác nhận E-voucher', value: 'APPLY_WAITTING_REQUEST' },
  { label: 'Khai báo E-voucher', value: 'WAITTING_DECLARE_REQUEST' },
  { label: 'Áp dụng E-voucher', value: 'EFFECTIVE_REQUEST' },
];
export const STATUS_LIQUIDATION = [
  { label: 'Khởi tạo', value: 'init' },
  { label: 'Chờ duyệt', value: 'waiting' },
  { label: 'Đã duyệt', value: 'approved' },
  { label: 'Đã từ chối', value: 'rejected' },
];

export enum MessageTypeEnum {
  Text = 1,
  Photo = 2,
  Video = 3,
  Audio = 4,
  File = 5,
  Link = 6,
  // Message dạng text có nội dung khi group được tạo
  CreateGroup = 7,
  // Message dạng text có nội dung khi group đổi tên.
  RenameGroup = 8,
  Location = 9,
  Contact = 10,
  Sticker = 11,
  Reply = 12,
  // Message kiểu thông báo (thêm thành viên, xoá thành viên).
  Notify = 100,
}
export const OPTIONS_APARTMENT_STATUS = [
  {
    label: 'Căn hộ sẵn sàng bàn giao',
    value: 'XD06',
    color: 'blue',
  },
  {
    label: 'Căn hộ đã bàn giao',
    value: 'XD04',
    color: 'green',
  },
  {
    label: 'Căn hộ đủ điều kiện làm sổ',
    value: 'XD08',
    color: 'gold',
  },
  {
    label: 'Căn hộ đang làm sổ',
    value: 'XD09',
    color: 'magenta',
  },
  {
    label: 'Căn hộ đã có sổ đợi bàn giao',
    value: 'XD10',
    color: 'purple',
  },
  {
    label: 'Căn hộ đã bàn giao sổ',
    value: 'XD11',
    color: 'cyan',
  },
];

export const OPTIONS_HANDOVER_STATUS = [
  {
    label: 'Chưa bàn giao',
    value: 'init',
  },
  {
    label: 'Đã bàn giao',
    value: 'handed',
  },
  {
    label: 'Bàn giao sau',
    value: 'later',
  },
  {
    label: 'Đã lên lịch',
    value: 'scheduled',
  },
];

export const OPTIONS_CERT_HANDOVER_STATUS = [
  {
    label: 'Căn hộ đủ điều kiện làm sổ',
    value: 'Eligible',
  },
  {
    label: 'Căn hộ đang làm sổ',
    value: 'Cer_in_process',
  },
  {
    label: 'Căn hộ đã có sổ đợi bàn giao',
    value: 'Cer_ready_handover',
  },
  {
    label: 'Căn hộ đã bàn giao sổ',
    value: 'Cer_handed_over',
  },
];

export const DEPOSIT_CONTRACT_FORM = {
  NO_DEPOSIT: 'NO_DEPOSIT',
  GOODWILL_DEPOSIT: 'GOODWILL_DEPOSIT',
  COMMITTED_DEPOSIT: 'COMMITTED_DEPOSIT',
};

export const ORGCHART_TYPE = {
  INTERNAL: 'INTERNAL',
  EXTERNAL: 'EXTERNAL',
};

export const DEPOSIT_CONTRACT_FORM_NAME = {
  [DEPOSIT_CONTRACT_FORM.NO_DEPOSIT]: 'Không ký quỹ',
  [DEPOSIT_CONTRACT_FORM.GOODWILL_DEPOSIT]: 'Ký quỹ thiện chí',
  [DEPOSIT_CONTRACT_FORM.COMMITTED_DEPOSIT]: 'Ký quỹ cam kết',
};

export const ORGCHART_TYPE_NAME = {
  [ORGCHART_TYPE.INTERNAL]: 'Đơn vị nội bộ',
  [ORGCHART_TYPE.EXTERNAL]: 'Đơn vị ĐTHT',
};

export const DEBT_REPORT_STATUS = {
  assigned: { label: 'Đã phân bổ', color: 'gray' },
  note: { label: 'Ghi chú', color: 'blue' },
  call: { label: 'Ghi nhận cuộc gọi', color: 'blue' },
  done: { label: 'Hoàn thành', color: 'green' },
};

export const OPTIONS_STATUS_DEBT_REMINDER = [
  { label: 'Chưa đến hạn', value: 'Chưa đến hạn', color: '#1677FF' },
  { label: 'Sắp đến hạn', value: 'Sắp đến hạn', color: '#FAAD14' },
  { label: 'Đang ân hạn', value: 'Đang ân hạn', color: '#13C2C2' },
  { label: 'Đến hạn', value: 'Đến hạn', color: '#FA8C16' },
  { label: 'Trễ hạn', value: 'Trễ hạn', color: '#FF4D4F' },
];

export const OPTIONS_STATUS_ASSIGN_DEBT_REPORT = [
  { label: 'Đang phân bổ', value: 'pending', color: '#1677FF' },
  { label: 'Đã phân bổ', value: 'assigned', color: '#FAAD14' },
];

export const OPTIONS_STATUS_PAYMENT_DEBT_REPORT = [
  { label: 'Chưa thanh toán', value: 'Chưa thanh toán', color: '#FF4D4F' },
  { label: 'Đã thanh toán 1 phần', value: 'Đã thanh toán 1 phần', color: '#FAAD14' },
  { label: 'Đã thanh toán', value: 'Đã thanh toán 1 phần', color: '#52C41A' },
];

export const OPTIONS_TYPE_PRODUCT_DEBT_REPORT = [
  { label: 'Căn hộ', value: 'Căn hộ' },
  { label: 'Shophouse', value: 'Shophouse' },
  { label: 'Duplex', value: 'Duplex' },
  { label: 'Biệt thự liền kề', value: 'Biệt thự liền kề' },
  { label: 'Đất nền', value: 'Đất nền' },
];

export const OPTIONS_STATUS_INTEREST_CALCULATION = [
  { label: 'Chưa thanh toán', value: 'INIT' },
  { label: 'Hoàn thành thanh toán', value: 'TRANSFERED' },
];

export const OPTIONS_TYPE_PROPOSAL_INTEREST_CALCULATION = [
  { label: 'Tờ trình đề nghị xem xét miễn/giảm lãi', value: 'PROPOSAL_FOR_INTEREST_WAIVER_OR_REDUCTION' },
  {
    label: 'Tờ trình đề nghị loại trừ hợp đồng khỏi danh mục công nợ',
    value: 'PROPOSAL_FOR_EXCLUDING_CONTRACT_FROM_DEBT_LIST',
  },
  {
    label: 'Tờ trình đề nghị phục hồi hợp đồng vào danh sách công nợ',
    value: 'PROPOSAL_FOR_REINSTATING_CONTRACT_INTO_DEBT_LIST',
  },
];

export const OPTIONS_MARKETING_STATUS = [
  { label: 'Đạng soạn thảo', name: 'Đạng soạn thảo', value: 'NEW', color: '#FAAD14' },
  { label: 'Đang phê duyệt', name: 'Đang phê duyệt', value: 'WAITING', color: '#1677FF' },
  { label: 'Đã duyệt', name: 'Đã duyệt', value: 'APPROVED', color: '#389E0D' },
  { label: 'Thu hồi', name: 'Thu hồi', value: 'RETURNED', color: '#FF4D4F' },
  { label: 'Trả về', name: 'Trả về', value: 'REJECTED', color: '#FF4D4F' },
  { label: 'Hủy', name: 'Hủy', value: 'CANCELLED ', color: '#FF4D4F' },
];

export const OPTIONS_MARKETING_TYPE = [
  { label: 'Căn hộ', value: 'department' },
  { label: 'Dự án', value: 'project' },
];

export const TYPE_BLOCK = [
  {
    value: 'BLOCK',
    label: 'Căn hộ',
  },
  {
    value: 'ZONE',
    label: 'Thấp tầng',
  },
];
