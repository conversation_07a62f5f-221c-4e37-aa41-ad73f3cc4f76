import { create } from 'zustand';
import {
  THandover,
  TOwnershipCertificateConfigGen,
  TOwnershipCertificateConfigNoti,
} from '../../types/ownershipCertificateConfig';

interface IStoreOwnershipCertificateConfig {
  openModalCreate: boolean;
  dataForEligibles?: THandover[];
  dataCerInProcesss?: THandover[];
  dataForCerReadyHandovers?: THandover[];

  initialValueGen?: TOwnershipCertificateConfigGen;
  initialValueNoti?: TOwnershipCertificateConfigNoti;
  isModified?: boolean;
  setDataForEligibles: (data: THandover[]) => void;
  setDataCerInProcesss: (data: THandover[]) => void;
  setDataForCerReadyHandovers: (data: THandover[]) => void;
  setOpenModalCreate: (openModalCreate: boolean) => void;
  setInitialValueGen: (initialValueGen: TOwnershipCertificateConfigGen) => void;
  setInitialValueNoti: (initialValueNoti: TOwnershipCertificateConfigNoti) => void;
  setIsModified: (isModified: boolean) => void;
}

export const useStoreOwnershipCertificateConfig = create<IStoreOwnershipCertificateConfig>(set => ({
  openModalCreate: false,
  dataForEligibles: undefined,
  dataCerInProcesss: undefined,
  dataForCerReadyHandovers: undefined,
  isModified: false,
  initialValue: undefined,
  setDataForEligibles: action => set({ dataForEligibles: action }),
  setDataCerInProcesss: action => set({ dataCerInProcesss: action }),
  setDataForCerReadyHandovers: action => set({ dataForCerReadyHandovers: action }),
  setOpenModalCreate: action => set({ openModalCreate: action }),
  setInitialValueGen: action => set({ initialValueGen: action }),
  setInitialValueNoti: action => set({ initialValueNoti: action }),
  setIsModified: action => set({ isModified: action }),
}));

export const defaultEmailTemplateForEligible = `
  <p>Kính gửi Quý khách hàng {{customer.name}},</p>
  <p>Căn hộ {{property.code}} của Quý khách đã đủ điều kiện tiến hành thủ tục cấp Giấy chứng nhận quyền sở hữu (sổ hồng).</p>
  <p>Để hoàn tất hồ sơ, Quý khách vui lòng chuẩn bị đầy đủ các giấy tờ theo checklist hướng dẫn.</p>
  <p>Mọi thắc mắc, xin liên hệ {{hotline}} để được hỗ trợ.</p>
  <p>Trân trọng,</p>
  <p>Công ty cổ phần tập đoàn Đất Xanh</p>
`;

export const defaultEmailTemplateCerHandedOver = `
<p>Kính gửi Quý Khách hàng {{customer.name}},</p>
<p>Công ty cổ phần tập đoàn Đất Xanh trân trọng cảm ơn Quý khách đã tin tưởng và đồng hành cùng chúng tôi trong suốt thời gian qua.</p>
<p>Chúng tôi xin thông báo:<br />Sản phẩm {{property.code}} thuộc dự án {{project.name}} của Quý khách đã hoàn tất thủ tục cấp sổ và hiện đang trong quá trình chuẩn bị bàn giao.</p>
<p>Trong thời gian tới, bộ phận Chăm sóc Khách hàng của chúng tôi sẽ liên hệ trực tiếp với Quý khách để sắp xếp lịch hẹn bàn giao sổ hồng.</p>
<p>Nếu Quý khách có bất kỳ thắc mắc hay cần hỗ trợ, vui lòng liên hệ:<br />📞 Hotline: {{hotline}}</p>
<p>Trân trọng cảm ơn Quý khách và kính chúc Quý khách luôn mạnh khỏe, thành công và hạnh phúc.</p>
<p>Trân trọng,</p>
<p>Công ty cổ phần tập đoàn Đất Xanh</p>
`;

export const defaultEmailTemplateForCerReadyHandover = `
<p>Kính gửi Quý khách hàng {{customer.name}},</p>
<p>Chúng tôi trân trọng thông báo: Quý khách đã nhận bàn giao Giấy chứng nhận quyền sở hữu cho căn hộ {{property.code}}.</p>
<p>Xin chân thành cảm ơn Quý khách đã đồng hành cùng chúng tôi.<br />Chúc Quý khách nhiều sức khỏe và thành công!</p>
<p>Trân trọng,</p>
<p>Công ty cổ phần tập đoàn Đất Xanh</p>
`;
