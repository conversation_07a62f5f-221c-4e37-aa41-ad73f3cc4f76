import { But<PERSON>, Form, Spin } from 'antd';
import BreadCrumbComponent from '../../../components/breadCrumb';
import { useFetch, useUpdateField } from '../../../hooks';
import { useNavigate, useParams } from 'react-router-dom';
import { PRICE_COEFFICIENT } from '../../../configs/path';
import { getPriceCoefficientsDetail, updatePriceCoefficients } from '../../../service/priceCoefficient';
import PriceCoefficientForm from '../components/PriceCoefficientForm';
import { CreatePriceCoefficientFormValues, IPriceCoefficients } from '../../../types/priceCoefficient';

export default function PriceCoefficientsDetail() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const [form] = Form.useForm<CreatePriceCoefficientFormValues>();
  const { data, isFetching } = useFetch<IPriceCoefficients>({
    api: () => getPriceCoefficientsDetail(id!),
    queryKeyArr: ['price-coefficients-detail', id],
    enabled: !!id,
    withFilter: false,
    cacheTime: 10,
  });

  const detail = data?.data?.data as IPriceCoefficients;

  const { mutateAsync: updateProperty, isPending } = useUpdateField({
    apiQuery: updatePriceCoefficients,
    keyOfListQuery: ['price-coefficients'],
    messageSuccess: 'Chỉnh sửa cấu hình giá thành công',
  });

  const handleFinish = async (values: CreatePriceCoefficientFormValues) => {
    const newValues = {
      ...values,
      name: values.name.trim(),
    };

    const res = await updateProperty({
      payload: newValues,
      id: id ?? '',
    });
    if (res?.data?.statusCode === '0') {
      navigate(PRICE_COEFFICIENT);
      form.resetFields();
    }
  };

  return (
    <Spin spinning={isFetching} className="price-coefficient">
      <BreadCrumbComponent titleBread={detail?.name} />

      {isFetching || (
        <PriceCoefficientForm
          form={form}
          onFinish={handleFinish}
          initialValues={{
            ...detail,
          }}
        />
      )}

      <div style={{ display: 'flex', justifyContent: 'flex-end', gap: 8, marginTop: 32 }}>
        <Button type="default" onClick={() => form.resetFields()}>
          Huỷ
        </Button>
        <Button type="primary" onClick={() => form.submit()} loading={isPending}>
          Lưu thay đổi
        </Button>
      </div>
    </Spin>
  );
}
