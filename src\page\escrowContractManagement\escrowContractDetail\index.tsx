import React from 'react';
import { Tabs, Typography } from 'antd';
import BreadCrumbComponent from '../../../components/breadCrumb';
import EscrowContractForm from '../component/escrowContractForm';
import PaymentTracking from '../component/paymentTracking';
import { getDetailDepositContract, updateDepositContract } from '../../../service/depositContract';
import { useFetch, useUpdateField } from '../../../hooks';
import { EscrowContract } from '../../../types/depositContract';
import { useParams } from 'react-router-dom';
import './styles.scss';
import FPTLogo from '../../../assets/images/FPT_logo.png';
const { Title, Text } = Typography;

const EscrowContractDetail: React.FC = () => {
  const { id } = useParams();

  const { data } = useFetch<EscrowContract>({
    queryKeyArr: ['get-detail-deposit-contract'],
    api: () => id && getDetailDepositContract(id),
    enabled: !!id,
    cacheTime: 10,
  });

  const initialValues = data?.data?.data;

  const { mutateAsync: update, isPending: isUpdateLoading } = useUpdateField<EscrowContract>({
    keyOfListQuery: ['get-deposit-contract'],
    keyOfDetailQuery: ['get-detail-deposit-contract'],
    apiQuery: updateDepositContract,
    isMessageError: false,
    messageSuccess: 'Chỉnh sửa hợp đồng ký quỹ thành công',
  });

  const handleUpdateEscrowContract = async (values: EscrowContract) => {
    await update({ ...values, id: id });
  };

  return (
    <div className="deposit-contract-detail-page">
      <BreadCrumbComponent />
      {id && (
        <div className="project-card">
          <div className="project-info">
            <Title level={5}>{`Hợp đồng ký quỹ ${initialValues?.code || ''}`}</Title>
            <Text type="secondary">
              Dự án: <span className="text-type">{initialValues?.project?.name || ''}</span>
            </Text>
          </div>

          <div className="project-image">
            <img
              src={
                initialValues?.imageUrl ? `${import.meta.env.VITE_S3_IMAGE_URL}/${initialValues?.imageUrl}` : FPTLogo
              }
              alt="Project"
            />
          </div>
        </div>
      )}
      <Tabs
        defaultActiveKey="1"
        items={[
          {
            key: '1',
            label: 'Thông tin chung',
            children: (
              <EscrowContractForm
                initialValues={initialValues}
                onFinish={handleUpdateEscrowContract}
                loading={isUpdateLoading}
              />
            ),
          },
          {
            key: '2',
            label: 'Theo dõi tiến độ thanh toán',
            children: (
              <PaymentTracking installments={initialValues?.installments} totalAmount={initialValues?.depositAmount} />
            ),
          },
        ]}
      />
    </div>
  );
};

export default EscrowContractDetail;
