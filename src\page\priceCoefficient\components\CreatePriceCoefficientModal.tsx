import { Modal, Form } from 'antd';
import PriceCoefficientForm from './PriceCoefficientForm';
import { CreatePriceCoefficientFormValues } from '../../../types/priceCoefficient';
import { createPriceCoefficients } from '../../../service/priceCoefficient';
import { useCreateField } from '../../../hooks';

interface CreatePriceCoefficientModalProps {
  open: boolean;
  onCancel: () => void;
  onOk?: (values: CreatePriceCoefficientFormValues) => void;
}

export default function CreatePriceCoefficientModal({ open, onCancel, onOk }: CreatePriceCoefficientModalProps) {
  const [form] = Form.useForm<CreatePriceCoefficientFormValues>();
  const { mutateAsync: createPriceCoefficient, isPending } = useCreateField({
    apiQuery: createPriceCoefficients,
    keyOfListQuery: ['price-coefficients'],
    messageSuccess: 'Tạo mới cấu hình giá thành công',
  });

  const handleFinish = async (values: CreatePriceCoefficientFormValues) => {
    const result: CreatePriceCoefficientFormValues = {
      ...values,
    };

    const res = await createPriceCoefficient(result);
    if (res?.data?.statusCode === '0') {
      form.resetFields();
      onCancel();
      onOk?.(result);
    }
    form.resetFields();
  };

  return (
    <Modal
      wrapClassName="wrapper-modal-lead-common price-coefficient__modal"
      open={open}
      title="Tạo mới hệ số"
      width={1440}
      okButtonProps={{ loading: isPending }}
      onCancel={onCancel}
      onOk={() => {
        form.submit();
      }}
      destroyOnClose
    >
      <PriceCoefficientForm form={form} onFinish={handleFinish} />
    </Modal>
  );
}
