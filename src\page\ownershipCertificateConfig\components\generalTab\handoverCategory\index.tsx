import { useState } from 'react';

import ModalHandover from './ModalHandover';
import './style.scss';
import TableAddCategory from './TableAddCategory';
import { THandover } from '../../../../../types/ownershipCertificateConfig';
import { useStoreOwnershipCertificateConfig } from '../../../storeOwnershipCertificateConfig';

const HandoverCategory = () => {
  const [openModalHandover, setOpenModalHandover] = useState(false);
  const [defaultCategory, setDefaultCategory] = useState<THandover>();
  const [typeTableAddCategory, setTypeTableAddCategory] = useState<string>('');

  const {
    dataForCerReadyHandovers,
    dataCerInProcesss,
    dataForEligibles,
    setDataForCerReadyHandovers,
    setDataCerInProcesss,
    setDataForEligibles,
  } = useStoreOwnershipCertificateConfig();

  const handleOpenModal = (type: string) => {
    setTypeTableAddCategory(type);
    setOpenModalHandover(true);
  };
  const handleCloseModal = () => {
    setOpenModalHandover(false);
    setDefaultCategory(undefined);
  };

  return (
    <>
      <h3>Checklist bàn giao sổ</h3>
      <TableAddCategory
        titleSessionAddCategory="Sản phẩm đủ điều kiện làm sổ"
        dataCategory={dataForEligibles as THandover[]}
        setDataCategory={setDataForEligibles}
        openModalHandover={handleOpenModal}
        setDefaultCategory={setDefaultCategory}
        type={'FOR_ELIGIBLE'}
      />
      <TableAddCategory
        titleSessionAddCategory="Sản phẩm đang làm sổ"
        dataCategory={dataCerInProcesss as THandover[]}
        setDataCategory={setDataCerInProcesss}
        openModalHandover={handleOpenModal}
        setDefaultCategory={setDefaultCategory}
        type={'CER_IN_PROCESS'}
      />
      <TableAddCategory
        titleSessionAddCategory="Sản phẩm đã có sổ - đợi bàn giao"
        dataCategory={dataForCerReadyHandovers as THandover[]}
        setDataCategory={setDataForCerReadyHandovers}
        openModalHandover={handleOpenModal}
        setDefaultCategory={setDefaultCategory}
        type={'FOR_CER_READY_HANDOVER'}
      />
      <ModalHandover
        open={openModalHandover}
        onCancel={handleCloseModal}
        defaultValue={defaultCategory}
        dataCategory={
          typeTableAddCategory === 'FOR_ELIGIBLE'
            ? dataForEligibles
            : typeTableAddCategory === 'CER_IN_PROCESS'
              ? dataCerInProcesss
              : dataForCerReadyHandovers
        }
        setDataCategory={
          typeTableAddCategory === 'FOR_ELIGIBLE'
            ? setDataForEligibles
            : typeTableAddCategory === 'CER_IN_PROCESS'
              ? setDataCerInProcesss
              : setDataForCerReadyHandovers
        }
      />
    </>
  );
};

export default HandoverCategory;
