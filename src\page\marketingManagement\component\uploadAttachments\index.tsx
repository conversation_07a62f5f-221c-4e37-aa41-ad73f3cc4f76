import { UploadOutlined } from '@ant-design/icons';
import { Button, Form, notification, Upload } from 'antd';
import { RcFile } from 'antd/lib/upload';
import { AxiosError, AxiosResponse } from 'axios';
import { useEffect, useState } from 'react';
import { ALLOWED_ATTACHMENT_EXTENSIONS } from '../../../../constants/common';

import { v4 as uuidv4 } from 'uuid';
import { IMarketing, TAttachment } from '../../../../types/marketing';

interface UploadAttachmentsProps {
  apiUpload: (files: RcFile[]) => Promise<AxiosResponse>;
  fieldAttachments?: string;
  initValue?: IMarketing;
}

const UploadAttachments = (props: UploadAttachmentsProps) => {
  const { apiUpload, fieldAttachments, initValue } = props;
  const form = Form.useFormInstance();

  const [files, setFiles] = useState<TAttachment[]>();
  const [fileList, setFileList] = useState<TAttachment[]>([]); //Use to check Error and LoadingTable

  const [isLoadingUpload, setIsLoadingUpload] = useState<boolean>(false);

  useEffect(() => {
    form?.setFieldsValue({ [fieldAttachments as string]: files });
  }, [fieldAttachments, files, form]); // Cập nhật Form khi files thay đổi

  useEffect(() => {
    if (initValue) {
      const initFiles = initValue?.attachments?.map(item => {
        const link = `${import.meta.env.VITE_S3_IMAGE_URL}/${item}`;
        return {
          name: item,
          url: link,
          uid: uuidv4(),
          fileUrl: item,
        };
      });
      setFiles(initFiles as TAttachment[]);
    }
  }, [initValue]);

  const handleBeforeUpload = async (file: RcFile, fileList: RcFile[]) => {
    if (fileList[0] === file) {
      setIsLoadingUpload(true);
    }

    const fileName = file.name.toLowerCase();
    const isAllowedType = ALLOWED_ATTACHMENT_EXTENSIONS.some(ext => fileName.endsWith(ext));

    // Tổng số lượng file sau khi upload
    const totalFiles = (files?.length || 0) + fileList?.length;

    if (totalFiles > 10) {
      if (fileList[fileList.length - 1] === file) {
        notification.error({
          message: `Tải lên vượt quá 10 file!`,
        });
      }
      setIsLoadingUpload(false);
      return Upload.LIST_IGNORE;
    }

    // Check tổng dung lượng tất cả file
    const currentTotalSize =
      (files?.reduce((acc, attachment) => acc + (attachment?.file?.size || 0), 0) || 0) +
      fileList.reduce((acc, file) => acc + (file?.size || 0), 0);

    const totalSizeMB = currentTotalSize / (1024 * 1024); // Byte -> MB
    if (totalSizeMB > 1024) {
      if (fileList[fileList.length - 1] === file) {
        notification.error({
          message: `Tổng dung lượng vượt quá 1GB!`,
        });
      }
      setIsLoadingUpload(false);
      return Upload.LIST_IGNORE;
    }

    // Check dung lượng từng file
    const fileSizeMB = file?.size / (1024 * 1024); // Byte -> MB

    if (fileSizeMB > 100) {
      if (fileList[fileList.length - 1] === file) {
        notification.error({
          message: `Kích thước file "${file?.name}" vượt quá 100MB!`,
        });
      }
      setIsLoadingUpload(false);
      return Upload.LIST_IGNORE;
    }

    if (!isAllowedType) {
      if (fileList[fileList.length - 1] === file) {
        notification.error({
          message:
            'File không đúng định dạng. Vui lòng sử dụng file .png, .jpg, .jpeg, .mp4, .avi, .mov, .wmv, .xls, .xlsx, .doc, .docx, .pdf, .ppt, .pptx, .jfif, .rar, .zip, .msg hoặc .txt',
        });
      }
      setIsLoadingUpload(false);
      return Upload.LIST_IGNORE;
    }

    if (fileList[fileList.length - 1] === file) {
      setFileList(fileList);
    }

    return true;
  };
  return (
    <Form.Item
      name={[fieldAttachments as string]}
      valuePropName="fileList"
      getValueFromEvent={e => {
        return Array.isArray(e) ? e : e?.fileList;
      }}
    >
      <Upload
        beforeUpload={handleBeforeUpload}
        maxCount={10}
        multiple
        customRequest={async ({ file, onSuccess, onError }) => {
          try {
            const fileData = file as RcFile;
            const resp = await apiUpload([fileData]);
            const data = resp.data.data;

            if (data) {
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              const newFiles = {
                name: data?.fileUrl,
                url: data?.fileUrl,
                id: data?.uid || uuidv4(),
                uid: data?.uid || uuidv4(),
                key: data?.key || uuidv4(),
                fileUrl: data?.fileUrl,
              };

              setFiles(prev => {
                const updatedAttachments = [...(prev || []), newFiles];
                form?.setFieldValue([fieldAttachments as string], updatedAttachments);
                return updatedAttachments;
              });
            }
            const lastFile = fileList[0];

            if (fileData.uid === lastFile.uid) {
              setIsLoadingUpload(false);
            }

            onSuccess?.('ok');
          } catch (error: unknown) {
            setIsLoadingUpload(false);
            onError?.(error as AxiosError);
          }
        }}
        onRemove={file => {
          const newList = files?.filter(f => f.uid !== file.uid);
          setFiles(newList);
          form?.setFieldValue([fieldAttachments as string], newList);
          return true;
        }}
        fileList={files?.map(o => {
          const newFile = { ...o, uid: o?.uid ?? '1' };
          return newFile as RcFile;
        })} // gán với fileList
        name="file-upload"
      >
        <Button icon={<UploadOutlined />} disabled={isLoadingUpload}>
          Upload
        </Button>
      </Upload>
    </Form.Item>
  );
};

export default UploadAttachments;
