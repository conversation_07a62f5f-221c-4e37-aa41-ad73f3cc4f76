import { Col, Form, Input, Row, Select, Typography } from 'antd';
import React from 'react';
import { ORGCHART_TYPE } from '../../../../constants/common';
import {
  getListOrgchartExternal,
  getListOrgchartInternal,
  getListOrgchartPartner,
} from '../../../../service/depositContract';
import SingleSelectLazy from '../../../../components/select/singleSelectLazy';

const { Title } = Typography;
const { Option } = Select;

interface CustomerInfoProps {
  form: any;
  orgchartType: string;
  orgchartId: string | undefined;
  handleSelectOrgchartInternal: (values: any) => void;
  handleSelectOrgchartExternal: (values: any) => void;
  handleSelectOrgchartPartnerInternal: (values: any) => void;
  handleSelectOrgchartPartnerExternal: (values: any) => void;
}

const CustomerInfo: React.FC<CustomerInfoProps> = ({
  form,
  orgchartType,
  orgchartId,
  handleSelectOrgchartInternal,
  handleSelectOrgchartExternal,
  handleSelectOrgchartPartnerInternal,
  handleSelectOrgchartPartnerExternal,
}) => {
  return (
    <>
      <Title level={5}>Thông tin khách hàng</Title>
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label="Loại đơn vị"
            name={['orgchart', 'type']}
            rules={[{ required: true, message: 'Vui lòng chọn loại đơn vị' }]}
          >
            <Select placeholder="Chọn loại đơn vị" defaultValue={ORGCHART_TYPE.EXTERNAL}>
              <Option value={ORGCHART_TYPE.EXTERNAL}>Đơn vị ĐTHT</Option>
              <Option value={ORGCHART_TYPE.INTERNAL}>Đơn vị nội bộ</Option>
            </Select>
          </Form.Item>
        </Col>

        <Col span={12}>
          {orgchartType === ORGCHART_TYPE.INTERNAL ? (
            <Form.Item
              label="Tên đơn vị"
              name={['orgchart', 'name']}
              key="internal"
              rules={[{ required: true, message: 'Vui lòng chọn tên đơn vị' }]}
            >
              <SingleSelectLazy
                key="internal-select"
                apiQuery={getListOrgchartInternal}
                queryKey={['get-orgchart-internal']}
                keysLabel={['code', 'nameVN']}
                placeholder="Chọn tên đơn vị"
                handleSelect={handleSelectOrgchartInternal}
                enabled={orgchartType === ORGCHART_TYPE.INTERNAL}
                allowClear={true}
                defaultValues={{
                  value: form.getFieldValue(['orgchart', 'id']),
                  label: form.getFieldValue(['orgchart', 'name']),
                }}
              />
            </Form.Item>
          ) : (
            <Form.Item
              label="Tên đơn vị"
              name={['orgchart', 'name']}
              key="external"
              rules={[{ required: true, message: 'Vui lòng chọn tên đơn vị' }]}
            >
              <SingleSelectLazy
                key="external-select"
                apiQuery={getListOrgchartExternal}
                queryKey={['get-orgchart-external']}
                keysLabel={['partnershipCode', 'partnershipName']}
                placeholder="Chọn tên đơn vị"
                handleSelect={handleSelectOrgchartExternal}
                enabled={orgchartType === ORGCHART_TYPE.EXTERNAL}
                allowClear={true}
                defaultValues={{
                  value: form.getFieldValue(['orgchart', 'id']),
                  label: form.getFieldValue(['orgchart', 'name']),
                }}
              />
            </Form.Item>
          )}
        </Col>
        <Col span={12}>
          <Form.Item label="Mã đơn vị" name={['orgchart', 'code']}>
            <Input placeholder="Hiển thị mã đơn vị" maxLength={15} disabled />
          </Form.Item>
        </Col>

        <Col span={12}>
          <Form.Item label="Mã Business Partner" name={['orgchart', 'bpID']}>
            <Input placeholder="Hiển thị mã BP" maxLength={15} disabled />
          </Form.Item>
        </Col>

        <Col span={12}>
          <Form.Item label="Mã số thuế" name={['orgchart', 'taxCode']}>
            <Input placeholder="Hiển thị mã số thuế" maxLength={15} disabled />
          </Form.Item>
        </Col>

        <Col span={12}>
          <Form.Item
            label="Địa chỉ email"
            name={['orgchart', 'email']}
            rules={[{ type: 'email', message: 'Địa chỉ email sai định dạng' }]}
          >
            <Input placeholder="Nhập địa chỉ email" maxLength={254} />
          </Form.Item>
        </Col>

        <Col span={24}>
          {orgchartType === ORGCHART_TYPE.INTERNAL ? (
            <Form.Item label="Hợp tác với đơn vị bán hàng" name="orgchartPartnerId" key="partner-internal">
              <SingleSelectLazy
                key={`partner-internal-select-${orgchartId || 'empty'}`}
                apiQuery={getListOrgchartPartner}
                queryKey={['get-orgchart-partner-internal']}
                keysLabel={['code', 'nameVN']}
                placeholder="Chọn tên đơn vị bán hàng"
                handleSelect={handleSelectOrgchartPartnerInternal}
                moreParams={{ nids: orgchartId }}
                enabled={!!orgchartId}
                allowClear={true}
                defaultValues={{
                  value: form.getFieldValue(['orgchartPartner', 'id']),
                  label: form.getFieldValue(['orgchartPartner', 'name']),
                }}
              />
            </Form.Item>
          ) : (
            <Form.Item label="Hợp tác với đơn vị bán hàng" name="orgchartPartnerId" key="partner-external">
              <SingleSelectLazy
                key={`partner-external-select-${orgchartId || 'empty'}`}
                apiQuery={getListOrgchartPartner}
                queryKey={['get-orgchart-partner-external']}
                keysLabel={['code', 'nameVN']}
                placeholder="Chọn tên đơn vị bán hàng"
                handleSelect={handleSelectOrgchartPartnerExternal}
                moreParams={{ ids: orgchartId }}
                enabled={!!orgchartId}
                allowClear={true}
                defaultValues={{
                  value: form.getFieldValue(['orgchartPartner', 'id']),
                  label: form.getFieldValue(['orgchartPartner', 'name']),
                }}
              />
            </Form.Item>
          )}
        </Col>
      </Row>
    </>
  );
};

export default CustomerInfo;
