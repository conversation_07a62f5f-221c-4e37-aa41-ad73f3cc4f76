import { Button, Form, Modal } from 'antd';
import { useForm } from 'antd/es/form/Form';

interface ModalCreateProposalProps {
  open: boolean;
  onCancel: () => void;
  title?: string;
  description?: string;
  label?: string;
  path?: string;
  disable?: boolean;
  labelConfirm?: string;
  labelCancel?: string;
  showReasonField?: boolean; // Thêm prop để kiểm soát hiển thị Form.Item
  url?: string;
  onConfirm?: (proposalType: string) => void;
  typeProposal?: string;
}
const ModalConfirmCreateProposal = ({
  open,
  onCancel,
  title,
  labelCancel,
  description,
  labelConfirm = 'Xác nhận',
  onConfirm,
  typeProposal,
}: ModalCreateProposalProps) => {
  const [form] = useForm();

  return (
    <Modal
      className="modal-confirm-create-proposal"
      open={open}
      title={title}
      centered
      closable={false}
      okText="Tạo"
      cancelText="Huỷ"
      destroyOnClose
      width={480}
      footer={[
        <Button key="cancel" className="btn-cancel" type="text" onClick={onCancel}>
          {labelCancel || 'Huỷ'}
        </Button>,
        <Button
          key="confirm"
          className="btn-confirm"
          type="primary"
          onClick={() => onConfirm && onConfirm(typeProposal as string)}
        >
          {labelConfirm || 'Xác nhận'}
        </Button>,
      ]}
    >
      <Form form={form}>
        <p className="description">{description}</p>
      </Form>
    </Modal>
  );
};

export default ModalConfirmCreateProposal;
