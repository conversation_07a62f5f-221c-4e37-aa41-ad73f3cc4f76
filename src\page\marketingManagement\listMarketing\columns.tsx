import { TableColumnsType, Typography } from 'antd';
import { IMarketing } from '../../../types/marketing';
import dayjs from 'dayjs';
import { FORMAT_DATE_TIME, OPTIONS_MARKETING_STATUS } from '../../../constants/common';

const { Text } = Typography;

export const columns: TableColumnsType = [
  {
    title: 'Mã kế hoạch',
    dataIndex: 'marketingCode',
    width: '50%',
    key: 'marketingCode',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Mã chủ trương',
    dataIndex: 'eapProposalCode',
    width: '50%',
    key: 'eapProposalCode',
    render: (value: string, record) => {
      return (
        <Typography.Link
          onClick={() => {
            window.open(record?.eapUrl, '_blank', 'noopener noreferrer');
          }}
        >
          {value}
        </Typography.Link>
      );
    },
  },
  {
    title: '<PERSON>ên kế hoạch',
    dataIndex: 'marketingName',
    width: '50%',
    key: 'marketingName',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Mã dự án',
    dataIndex: 'projectCode',
    width: '45%',
    key: 'projectCode',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Tên dự án',
    dataIndex: 'projectName',
    width: '45%',
    key: 'projectName',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Mã ngân sách',
    dataIndex: 'costCenterCode',
    width: '45%',
    key: 'costCenterCode',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Tên ngân sách',
    dataIndex: 'costCenterName',
    width: '40%',
    key: 'costCenterName',
    render: (value: string) => (value ? value : '0'),
  },
  {
    title: 'Số tiền sau khi phê duyệt',
    dataIndex: 'approveAmount',
    width: '40%',
    key: 'approveAmount',
    render: (value: string) => (value ? value : '0'),
  },
  {
    title: 'Trạng thái kế hoạch',
    dataIndex: 'marketingStatus',
    width: '40%',
    key: 'marketingStatus',
    render: value => {
      const status = OPTIONS_MARKETING_STATUS.find(item => item.value === value);
      return status ? <Typography.Text style={{ color: status?.color }}>{status?.name}</Typography.Text> : '-';
    },
  },
  {
    title: 'Ngày tạo',
    dataIndex: 'createdDate',
    key: 'createdDate',
    width: 200,

    render: (value: string, record: IMarketing) => (
      <>
        <Text>{dayjs(value).format(FORMAT_DATE_TIME)}</Text>
        <br />
        <Text>{record?.createdBy ? `${record?.createdBy?.userName} - ${record?.createdBy?.fullName}` : '-'}</Text>
      </>
    ),
  },
  {
    title: 'Ngày cập nhật',
    dataIndex: 'updatedDate',
    key: 'updatedDate',
    width: 200,

    render: (value: string, record: IMarketing) => (
      <>
        <Text>{dayjs(value).format(FORMAT_DATE_TIME)}</Text>
        <br />
        <Text>{record?.updatedBy ? `${record?.updatedBy?.userName} - ${record?.updatedBy?.fullName}` : '-'}</Text>
      </>
    ),
  },
];
