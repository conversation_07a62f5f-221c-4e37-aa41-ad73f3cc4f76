import { PlusOutlined } from '@ant-design/icons';
import { Button, Table } from 'antd';
import { TableProps } from 'antd/lib';
import React, { useEffect, useMemo, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import './style.scss';
import { THandover } from '../../../../../types/ownershipCertificateConfig';
import { ActionsColumns } from '../../../../../components/table/components/ActionsColumns';

interface TableAddCategoryProps {
  titleSessionAddCategory: string;
  dataCategory: THandover[];
  setDataCategory: (data: THandover[]) => void;
  openModalHandover: (type: string) => void;
  setDefaultCategory: React.Dispatch<React.SetStateAction<THandover | undefined>>;
  type: 'FOR_ELIGIBLE' | 'CER_IN_PROCESS' | 'FOR_CER_READY_HANDOVER';
}

const TableAddCategory = (props: TableAddCategoryProps) => {
  const { titleSessionAddCategory, dataCategory, setDataCategory, openModalHandover, setDefaultCategory, type } = props;
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [formatHandovers, setFormatHandovers] = useState<THandover[]>([]);

  useEffect(() => {
    if (dataCategory) {
      setFormatHandovers(
        dataCategory?.map(item => ({
          ...item,
          children: item?.list?.map(childItem => ({
            ...childItem,
            id: uuidv4(),
          })),
        })),
      );
    }
  }, [dataCategory]);

  useEffect(() => {
    if (dataCategory?.length) {
      const newExpandedKeys = dataCategory?.map(item => item.id)?.filter(id => id !== undefined) as React.Key[];
      setExpandedKeys(newExpandedKeys);
    }
  }, [dataCategory]);

  const columnActions: TableProps['columns'] = useMemo(
    () => [
      {
        title: 'Hạng mục',
        dataIndex: 'name',
        width: '25%',
        key: 'name',
        render: value => value ?? value,
      },
      {
        title: 'Mô tả chi tiết',
        width: '35%',
        dataIndex: 'description',
        key: 'description',
        render: (value: string, record: THandover) => {
          if (record?.children) {
            return null;
          }
          return <div>{value ?? ''}</div>;
        },
      },
      {
        key: 'action',
        width: '5%',
        align: 'center',
        render: (_, record: THandover) => {
          if (!record?.children) {
            return null;
          }
          const handleModify = () => {
            openModalHandover(type);
            setDefaultCategory(record);
          };
          const categoryDelete = () => {
            const newData = dataCategory?.filter(item => item.id !== record.id);
            setDataCategory(newData || []);
          };

          return (
            <ActionsColumns
              overlayClassName="action-column-data-role"
              moreActions={[
                {
                  label: 'Chỉnh sửa',
                  key: 'modify',
                  onClick: handleModify,
                },
                {
                  label: 'Xóa',
                  key: 'delete',
                  onClick: categoryDelete,
                },
              ]}
            />
          );
        },
      },
    ],
    [dataCategory, openModalHandover, setDataCategory, setDefaultCategory, type],
  );

  useEffect(() => {
    if (dataCategory) {
      setFormatHandovers(
        dataCategory?.map(item => ({
          ...item,
          children: item?.list?.map(childItem => ({
            ...childItem,
            id: uuidv4(),
          })),
        })),
      );
    }
  }, [dataCategory]);

  useEffect(() => {
    if (dataCategory?.length) {
      const newExpandedKeys = dataCategory?.map(item => item.id)?.filter(id => id !== undefined) as React.Key[];
      setExpandedKeys(newExpandedKeys);
    }
  }, [dataCategory]);

  return (
    <div style={{ marginTop: '16px' }}>
      <h4>{titleSessionAddCategory}</h4>
      <Button icon={<PlusOutlined />} onClick={() => openModalHandover(type)} style={{ marginBottom: 16 }}>
        Thêm hạng mục
      </Button>

      <Table
        className="handover-table"
        columns={columnActions}
        dataSource={formatHandovers}
        pagination={false}
        rowKey="id"
        expandable={{
          expandedRowKeys: expandedKeys,
          expandIcon: () => null,
        }}
      />
    </div>
  );
};

export default TableAddCategory;
