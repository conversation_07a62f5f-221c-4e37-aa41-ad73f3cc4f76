import { useMutation } from '@tanstack/react-query';
import { Button, notification } from 'antd';
import { SearchProps } from 'antd/es/input';
import { Input, InputProps } from 'antd/lib';
import debounce from 'lodash/debounce';
import { useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';
import BreadCrumbComponent from '../../../components/breadCrumb';
import TableComponent from '../../../components/table';
import { TRAINING } from '../../../configs/path';
import { useFetch } from '../../../hooks';
import useFilter from '../../../hooks/filter';
import { exportListOfReportParticipants, getListOfReportParticipants } from '../../../service/trainingUser';
import { TCustomerAttendance } from '../../../types/trainingUser';
import { normalizeString } from '../../../utilities/regex';
import { downloadArrayBufferFile } from '../../../utilities/shareFunc';
import { columns } from './columns';
import './styles.scss';

const ListParticipantsReport = () => {
  const { id } = useParams();
  const [search, setSearch] = useState<string>();
  const [filter] = useFilter();

  const { data, isLoading } = useFetch<TCustomerAttendance[]>({
    queryKeyArrWithFilter: ['get-list-participants-users', id, search],
    api: getListOfReportParticipants,
    moreParams: { id, q: search },
    enabled: !!id,
  });

  const dataSource = data?.data?.data?.rows || [];

  const exportReportParticipantsMutation = useMutation({
    mutationFn: () => exportListOfReportParticipants(id, { q: search, ...filter }),
  });

  const handleSubmitExport = async () => {
    try {
      const response = await exportReportParticipantsMutation.mutateAsync();
      downloadArrayBufferFile({ data: response.data, fileName: `Bao cao tham du.xlsx` });
    } catch (error) {
      notification.error({ message: 'Xuất dữ liệu thất bại.' });
    }
  };

  const debouncedHandleSearch = useMemo(
    () =>
      debounce((searchTerm: string) => {
        const normalizeSearchTerm = normalizeString(searchTerm);
        setSearch(normalizeSearchTerm || undefined);
      }, 1000),
    [],
  );

  const handleChange: InputProps['onChange'] = event => {
    const searchTerm = event.target.value;
    debouncedHandleSearch(searchTerm);
  };
  const handleOnSearch: SearchProps['onSearch'] = value => {
    const normalizeSearchTerm = normalizeString(value);
    setSearch(normalizeSearchTerm || undefined);
  };

  return (
    <div className="wrapper-list-participants-report">
      <BreadCrumbComponent
        customItems={[
          {
            label: 'Chi tiết sự kiện',
            key: 'Chi tiết sự kiện',
            path: `${TRAINING}/${id}`,
          },
          {
            label: 'Báo cáo người tham dự',
            key: 'report-participants',
          },
        ]}
      />
      <div className="header-content">
        <Input.Search onChange={handleChange} onSearch={handleOnSearch} placeholder="Search" allowClear />
        <Button type="default" onClick={handleSubmitExport} loading={exportReportParticipantsMutation.isPending}>
          Tải xuống
        </Button>
      </div>
      <div className="table-participants-report">
        <TableComponent
          queryKeyArr={['get-list-participants-users', id, search]}
          columns={columns}
          loading={isLoading}
          dataSource={dataSource ?? []}
          rowKey="id"
          title={() => `Số lượng người tham dự: ${data?.data?.data?.total}`}
        />
      </div>
    </div>
  );
};

export default ListParticipantsReport;
