import { Form, Spin, Tabs } from 'antd';
import { TabsProps } from 'antd/lib';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { createSearchParams, useParams, useSearchParams } from 'react-router-dom';
import { v4 as uuidv4 } from 'uuid';
import BreadCrumbComponent from '../../../components/breadCrumb';
import ButtonOfPageDetail from '../../../components/button/buttonOfPageDetail';
import { FORMAT_DATE_API } from '../../../constants/common';
import { PRIMARY_CONTRACT_PERMISSION } from '../../../constants/permissions/primaryContract';
import { useCheckPermissions, useFetch, useUpdateField } from '../../../hooks';
import { useStoreOwnershipCertificateConfig } from '../storeOwnershipCertificateConfig';
import { formatItemsToTimeFrames, formatTimeFrameToSubmit, validateForms } from '../utilities';
import { OwnershipCertificateConfig, TOwnershipCertificateConfigGen } from '../../../types/ownershipCertificateConfig';
import {
  getByIdOwnershipCertificateConfig,
  updateOwnershipCertificateConfig,
} from '../../../service/ownershipCertificateConfig';
import NotificationTab from '../components/notificationTab';
import GeneralTab from '../components/generalTab';

const DetailOwnershipCertificateConfig = () => {
  const { id } = useParams();
  const [urlParams, setUrlParams] = useSearchParams();
  const filter = Object.fromEntries([...urlParams]);
  const [formGen] = Form.useForm();
  const [formNoti] = Form.useForm();
  const [tab, setTab] = useState(filter?.tab || 'general-info');
  const {
    setInitialValueGen,
    setInitialValueNoti,
    setIsModified,
    setDataForEligibles,
    setDataCerInProcesss,
    setDataForCerReadyHandovers,
    isModified,
    dataForEligibles,
    dataCerInProcesss,
    dataForCerReadyHandovers,
  } = useStoreOwnershipCertificateConfig();
  const { handoverUpdate } = useCheckPermissions(PRIMARY_CONTRACT_PERMISSION);

  const { data, isLoading } = useFetch<OwnershipCertificateConfig>({
    queryKeyArr: ['detail-ownership-certificate-config', id],
    api: () => getByIdOwnershipCertificateConfig(id),
    enabled: !!id,
  });
  const dataSource = data?.data?.data;

  const { mutateAsync, isPending } = useUpdateField({
    keyOfDetailQuery: ['detail-ownership-certificate-config', id],
    apiQuery: updateOwnershipCertificateConfig,
    isMessageError: false,
  });

  useEffect(() => {
    if (!dataSource) return;
    const timeFrames = formatItemsToTimeFrames(dataSource);

    const formatDataSourceGen: TOwnershipCertificateConfigGen = {
      accountingConfirm: dataSource?.accountingConfirm === 1 ? true : false,
      project: {
        id: dataSource?.project?.id,
        name: dataSource?.project?.name,
      },
      expectedDateRange: [dayjs(dataSource?.expectedStartDate), dayjs(dataSource?.expectedEndDate)], // Required fields for TOwnershipCertificateConfigGen
      status: dataSource?.status === 1 ? true : false,
      itemsForEligible: dataSource?.itemsForEligible || [],
      itemsCerInProcess: dataSource?.itemsCerInProcess || [],
      itemsForCerReadyHandover: dataSource?.itemsForCerReadyHandover || [],

      orgCharts: dataSource?.orgCharts || [],
      paymentPercent: dataSource?.paymentPercent?.toString() || '0',
      hotline: dataSource?.hotline || '',
      timeFrames: timeFrames || [],
      isActive: dataSource?.isActive === 1 ? true : false,
    };

    setInitialValueGen(formatDataSourceGen);
    formGen.setFieldsValue(formatDataSourceGen);

    const notificationData = {
      emailForEligible: {
        emailTemplate: dataSource?.emailForEligible?.emailTemplate,
        smsTemplate: dataSource?.emailForEligible?.smsTemplate,
        smsBrandName: dataSource?.emailForEligible?.smsBrandName,
        emailTitle: dataSource?.emailForEligible?.emailTitle,
        emailFrom: dataSource?.emailForEligible?.emailFrom,
        emailCC: dataSource?.emailForEligible?.emailCC,
        emailBCC: dataSource?.emailForEligible?.emailBCC,
      },
      emailCerHandedOver: {
        emailTemplate: dataSource?.emailCerHandedOver?.emailTemplate,
        smsTemplate: dataSource?.emailCerHandedOver?.smsTemplate,
        smsBrandName: dataSource?.emailCerHandedOver?.smsBrandName,
        emailTitle: dataSource?.emailCerHandedOver?.emailTitle,
        emailFrom: dataSource?.emailCerHandedOver?.emailFrom,
        emailCC: dataSource?.emailCerHandedOver?.emailCC,
        emailBCC: dataSource?.emailCerHandedOver?.emailBCC,
      },
      emailForCerReadyHandover: {
        emailTemplate: dataSource?.emailForCerReadyHandover?.emailTemplate,
        smsTemplate: dataSource?.emailForCerReadyHandover?.smsTemplate,
        smsBrandName: dataSource?.emailForCerReadyHandover?.smsBrandName,
        emailTitle: dataSource?.emailForCerReadyHandover?.emailTitle,
        emailFrom: dataSource?.emailForCerReadyHandover?.emailFrom,
        emailCC: dataSource?.emailForCerReadyHandover?.emailCC,
        emailBCC: dataSource?.emailForCerReadyHandover?.emailBCC,
      },
    };

    setInitialValueNoti(notificationData);
    formNoti.setFieldsValue(notificationData);
    setDataForEligibles(
      dataSource?.itemsForEligible?.map(item => ({
        ...item,
        id: item?.id ? item.id : uuidv4(),
      })) || [],
    );
    setDataCerInProcesss(
      dataSource?.itemsCerInProcess?.map(item => ({
        ...item,
        id: item?.id ? item.id : uuidv4(),
      })) || [],
    );
    setDataForCerReadyHandovers(
      dataSource?.itemsForCerReadyHandover?.map(item => ({
        ...item,
        id: item?.id ? item.id : uuidv4(),
      })) || [],
    );
  }, [
    dataSource,
    formGen,
    formNoti,
    setDataCerInProcesss,
    setDataForCerReadyHandovers,
    setDataForEligibles,
    setInitialValueGen,
    setInitialValueNoti,
  ]);

  const items: TabsProps['items'] = [
    {
      label: 'Thông tin chung',
      key: 'general-info',
      children: <GeneralTab form={formGen} />,
    },
    { label: 'Thông báo', key: 'notification', children: <NotificationTab form={formNoti} /> },
  ];
  const handleSubmit = async () => {
    try {
      const formResult = await validateForms(formGen, formNoti);
      if (!formResult) return;

      const { valuesGen, valuesNoti } = formResult;

      const formatExpectedDate = {
        expectedStartDate: dayjs(valuesGen?.expectedDateRange?.[0]).format(FORMAT_DATE_API),
        expectedEndDate: dayjs(valuesGen?.expectedDateRange?.[1]).format(FORMAT_DATE_API),
        expectedDateRange: undefined,
      };
      const formatTimeFrames = formatTimeFrameToSubmit(valuesGen?.timeFrames);

      const values = {
        ...valuesGen,
        ...valuesNoti,
        ...formatExpectedDate,
        id,
        itemsForEligible: dataForEligibles,
        itemsCerInProcess: dataCerInProcesss,
        itemsForCerReadyHandover: dataForCerReadyHandovers,
        status: valuesGen?.status ? 1 : 2,
        accountingConfirm: valuesGen?.accountingConfirm ? 1 : 2,
        paymentPercent: valuesGen?.paymentPercent && parseFloat(valuesGen?.paymentPercent),
        ...formatTimeFrames,
        timeFrames: undefined,
        isActive: valuesGen?.isActive ? 1 : 2,
      };
      const res = await mutateAsync(values);
      if (res?.data?.statusCode === '0') {
        setIsModified(false);
      }
    } catch (err) {
      console.error('Validation failed:', err);
    }
  };
  const handleCancel = () => {
    formGen.resetFields();
    formNoti.resetFields();
    setIsModified(false);
    setDataForEligibles([]);
    setDataCerInProcesss([]);
    setDataForCerReadyHandovers([]);
  };

  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  const handleChangeTab = (activeKey: string) => {
    const params = createSearchParams({ tab: activeKey });
    setTab(activeKey);
    setUrlParams(params);
  };

  return (
    <Spin spinning={isLoading}>
      <BreadCrumbComponent titleBread={dataSource?.project?.name} />
      <Tabs items={items} activeKey={tab} onChange={handleChangeTab} />
      {isModified && handoverUpdate && (
        <ButtonOfPageDetail handleSubmit={handleSubmit} handleCancel={handleCancel} loadingSubmit={isPending} />
      )}
    </Spin>
  );
};

export default DetailOwnershipCertificateConfig;
