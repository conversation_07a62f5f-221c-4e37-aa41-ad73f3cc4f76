import { Dayjs } from 'dayjs';
import { TCreatedBy } from '../common/common';

export type TListOfSalesPolicy = {
  id: string;
  name: string;
  code: string;
  projectName: string;
  createdDate: string;
  createdBy: TCreatedBy;
  modifiedDate: string;
  startDate: string;
  endDate: string;
  isUpdateStatus: boolean;
  modifiedBy: TCreatedBy;
  isActive: number;
};

export type TFilterSalesPolicy = {
  startCreatedDate?: string | Dayjs | null;
  endCreatedDate?: string | Dayjs | null;
  projectID?: string;
  search?: string;
};

export type TSalesPolicy = {
  name: string;
  code: string;
  projectID: string;
  startDate: string;
  endDate: string;
  active: string | number;
  amount?: number | string;
  analysisBonus?: string;
  analysisFees?: string;
  analysisIndicator?: string;
  comamount?: number | string;
  comments?: string;
  comrate?: number | string;
  date?: Dayjs[];
  isapplyVAT: string;
  listPos: TListDropdown[];
  listProjectId: TListDropdown[];
  policyType: string;
  revenueRate: number | string;
  listPayment: TPaymentBatch[];
  listBonus: TListBonus;
  projectName?: string;
  isUpdateStatus?: boolean;
  modifiedDate?: string;
  createdBy?: TCreatedBy;
  createdDate?: string;
  id?: string;
  reasonDelete?: string;
  modifiedBy?: TCreatedBy;
  periodFrom: string;
  periodTo: string;
  periodName: string;
  status: string | null;
  eappUrl?: string;
  eappNumber?: string;
  isActive?: number;
};

export type TPaymentBatch = {
  key: string;
  phase?: number | string;
  transactionRate?: number | string;
  status?: string;
  note?: string;
  isNew?: boolean;
};

export type TItemBonusByQuantity = {
  key: string;
  minProductsSold?: number | string;
  maxProductsSold?: number | string;
  bonusPercentage?: number | string;
  bonusAmount?: number | string;
  noteProductsSold?: string;
  isNew?: boolean;
};
export type TItemBonusByMargin = {
  key: string;
  minSalesPercentage?: number | string;
  maxSalesPercentage?: number | string;
  bonusPercentageOnRate?: number | string;
  bonusAmountOnRate?: number | string;
  noteSalesPercentage?: string;
  isNew?: boolean;
};

export type TListBonus = {
  isProgressiveByQuantity?: boolean;
  isProgressiveByMargin?: boolean;
  byQuantity?: TItemBonusByQuantity[];
  byMargin?: TItemBonusByMargin[];
};

export type TListDropdown = {
  name: string;
  id: string;
  code?: string;
};
