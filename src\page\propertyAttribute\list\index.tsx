import { Button, Flex } from 'antd';
import { useState } from 'react';
import BreadCrumbComponent from '../../../components/breadCrumb';
import TableComponent from '../../../components/table';
import { IPropertyAttribute } from '../../../types/propertyAttribute';
import { useFetch } from '../../../hooks';
import { getPropertyAttribute } from '../../../service/propertyAttribute';
import { columnsPropertyAttribute } from '../components/columns';
import FilterSearch from '../components/FilterSearch';
import CreatePropertyAttributeModal from '../components/CreatePropertyAttributeModal';

const PropertyAttributeList = () => {
  const [openCreate, setOpenCreate] = useState(false);

  const { data: { data: { data: { rows: data = [] } = {} } = {} } = {}, isFetching } = useFetch<IPropertyAttribute[]>({
    api: getPropertyAttribute,
    queryKeyArrWithFilter: ['property-attribute'],
  });

  const handleOpenModalCreate = () => {
    setOpenCreate(true);
  };

  const handleCloseModalCreate = () => {
    setOpenCreate(false);
  };

  return (
    <div className="wrapper-list">
      <BreadCrumbComponent />
      <div className="header-content">
        <FilterSearch />
        <Flex justify="end">
          <Button type="primary" onClick={handleOpenModalCreate}>
            Thêm thuộc tính
          </Button>
        </Flex>
      </div>

      <div className="table-list">
        <TableComponent
          queryKeyArr={['property-attribute']}
          columns={columnsPropertyAttribute}
          loading={isFetching}
          dataSource={data}
          rowKey="id"
        />
      </div>
      {openCreate && <CreatePropertyAttributeModal open={openCreate} onCancel={handleCloseModalCreate} />}
    </div>
  );
};

export default PropertyAttributeList;
