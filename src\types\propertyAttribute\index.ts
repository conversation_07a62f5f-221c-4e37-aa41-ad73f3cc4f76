export interface IPropertyAttribute {
  _id: string;
  id: string;
  allowedValues: string[];
  status: EPropertyAttributeStatus;
  softDelete: boolean;
  name: string;
  projectId: string;
  saleProgramId: string;
  code: string;
  createdBy: string;
  alias: string;
  createdDate: string;
  modifiedDate: string;
  __v: number;
  project: {
    _id: string;
    id: string;
    name: string;
  };
  saleProgram: {
    _id: string;
    id: string;
    name: string;
  };
}

export enum EPropertyAttributeStatus {
  Disabled = 0, // Vô hiệu
  Enabled = 1, // <PERSON><PERSON><PERSON> hoạt
}

export interface IPropertyAttributeFormValues {
  name: string;
  code?: string;
  projectId: string;
  saleProgramId: string;
  status: EPropertyAttributeStatus;
  allowedValues: string[];
}
