import { Checkbox, Col, Form, Input, Row, Switch, Typography } from 'antd';
import SingleSelectLazy from '../../../components/select/singleSelectLazy';
import { getProjectsForCommissionDebtPolicy } from '../../../service/commissionDebtPolicy';
import { ProjectDebtPolicy } from '../../../types/commissionDebtPolicy';
import { v4 as uuid } from 'uuid';
import dayjs from 'dayjs';
import { useParams } from 'react-router-dom';
import FormPeriodDebt from '../../../components/select/FormPeriodDebt';
import { FORMAT_DATE_TIME } from '../../../constants/common';
import { useCommissionDebtICommissionDebtPolicy } from '../storeDebtPolicy';

const { Title, Text } = Typography;
const { Item } = Form;

const GeneralInformation = () => {
  const form = Form.useFormInstance();
  const { id } = useParams();

  const { actionModal, setIsModified, initialValue, setDataPenaltySanctions } = useCommissionDebtICommissionDebtPolicy(
    state => state,
  );

  const handleSelectProject = (value: ProjectDebtPolicy) => {
    form.setFieldsValue({
      project: value
        ? {
            id: value.id,
            name: value.name,
            code: value.code,
            setting: value?.setting,
          }
        : undefined,
    });
    form.setFieldsValue({
      year: new Date().getFullYear().toString(),
      period: undefined,
    });
    if (value?.id === initialValue?.project?.id) {
      setDataPenaltySanctions(initialValue?.penalty?.map(item => ({ ...item, key: uuid() })));
    } else {
      setDataPenaltySanctions(
        value?.setting?.debtage?.map(item => ({
          key: uuid(),
          amount: 0,
          type: undefined,
          name: item?.name,
          unit: 'VNĐ',
        })) || [],
      );
    }
    !!id && setIsModified(true);
  };

  return (
    <Row gutter={{ md: 24, lg: 40, xl: 80, xxl: 126 }}>
      <Col span={24}>
        <Title level={5}>Thông tin chung</Title>
      </Col>
      <Col xs={24} md={12}>
        <Row gutter={24}>
          <Col xs={24} md={12}>
            <Item label="Mã bộ chỉ tiêu KPI" name="code">
              <Input placeholder="Hệ thống tự động hiển thị" disabled />
            </Item>
          </Col>
          <Col xs={24} md={12}>
            <Item
              label="Tên bộ chỉ tiêu KPI"
              name="name"
              required
              rules={[
                {
                  required: true,
                  message: 'Vui lòng nhập tên bộ chỉ tiêu',
                  whitespace: true,
                },
              ]}
            >
              <Input placeholder="Nhập tên bộ chỉ tiêu" maxLength={255} />
            </Item>
          </Col>

          <Col xs={24} md={12}>
            <Item label="Dự án" name="project" rules={[{ required: true, message: 'Vui lòng chọn dự án' }]}>
              <SingleSelectLazy
                gcTime={1000 * 60 * 15} // 15 phuts
                staleTime={1000 * 60 * 5} // 5 phút
                apiQuery={getProjectsForCommissionDebtPolicy}
                queryKey={['list-projects']}
                enabled={actionModal?.isOpen || true}
                placeholder="Chọn dự án"
                keysLabel={['code', 'name']}
                handleSelect={handleSelectProject}
                defaultValues={{
                  value: form.getFieldValue(['project', 'id']),
                  label: form.getFieldValue(['project', 'name']),
                }}
              />
            </Item>
          </Col>
          <Col xs={24} md={12}>
            <Item label=" " name={'isInterestSupport'} valuePropName="checked">
              <Checkbox>Hỗ trợ vay lãi suất</Checkbox>
            </Item>
          </Col>
          <Col xs={24}>
            <FormPeriodDebt required label="Kỳ thiết lập" fieldName="project" />
          </Col>

          <Col xs={24} md={12}>
            <Form.Item
              label="Trạng thái"
              layout="horizontal"
              labelCol={{ span: 12 }}
              labelAlign="left"
              name="isActive"
              valuePropName="checked"
              getValueFromEvent={checked => (checked ? 1 : 2)}
              getValueProps={value => ({
                checked: value === 1,
              })}
            >
              <Switch />
            </Form.Item>
          </Col>

          <Col xs={24} md={12}>
            <Form.Item shouldUpdate>
              {({ getFieldValue }) => {
                const status = getFieldValue('isActive');
                return (
                  <span style={{ color: status === 1 ? '#389E0D' : '#CF1322' }}>
                    {status === 1 ? 'Đã kích hoạt' : 'Vô hiệu hóa'}
                  </span>
                );
              }}
            </Form.Item>
          </Col>
          <Col xs={24} md={12}>
            <Item label="Số E-approve" name={'eappNumber'}>
              <Input disabled />
            </Item>
          </Col>
          <Col xs={24} md={12}>
            <Item label="Trạng thái phê duyệt" name={'status'}>
              <Input disabled />
            </Item>
          </Col>
        </Row>
      </Col>
      <Col sm={12} xs={24}>
        <Row gutter={24}>
          <Col lg={6} xs={8}>
            <Text disabled>Ngày cập nhật: </Text>
          </Col>
          <Col lg={18} xs={16}>
            <Text disabled>
              {dayjs(initialValue?.createdDate).format(FORMAT_DATE_TIME)}&nbsp;&nbsp;
              {`${initialValue?.modifiedBy?.userName} - ${initialValue?.modifiedBy?.fullName}`}
            </Text>
          </Col>
        </Row>
        <Row gutter={24}>
          <Col lg={6} xs={8}>
            <Text disabled>Ngày tạo: </Text>
          </Col>
          <Col lg={18} xs={16}>
            <Text disabled>
              {dayjs(initialValue?.modifiedDate).format(FORMAT_DATE_TIME)}&nbsp;&nbsp;
              {`${initialValue?.createdBy?.userName} - ${initialValue?.createdBy?.fullName}`}
            </Text>
          </Col>
        </Row>
      </Col>
    </Row>
  );
};

export default GeneralInformation;
