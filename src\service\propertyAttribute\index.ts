import { getRequest, postRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import { IPropertyAttributeFormValues } from '../../types/propertyAttribute';

export const getPropertyAttribute = async (payload: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/attribute`,
    payload as Record<string, unknown> | undefined,
  );

  return response;
};

export const createPropertyAttribute = async (payload: IPropertyAttributeFormValues) => {
  const response = await postRequest(`${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/attribute`, payload);

  return response;
};

export const updatePropertyAttribute = async ({
  payload,
  id,
}: {
  payload: IPropertyAttributeFormValues;
  id: string;
}) => {
  const response = await putRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/attribute/${id}`,
    payload,
  );

  return response;
};

export const getPropertyAttributeDetail = async (id: string) => {
  const response = await getRequest(`${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/attribute/${id}`);

  return response;
};
