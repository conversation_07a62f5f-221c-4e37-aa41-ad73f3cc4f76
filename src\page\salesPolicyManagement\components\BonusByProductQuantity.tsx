import { PlusOutlined } from '@ant-design/icons';
import { Button, Checkbox, Flex, Form, Input, Typography } from 'antd';
import { Space } from 'antd/lib';
import React, { useEffect, useState } from 'react';
import { v4 as uuid } from 'uuid';
import { ColumnTypesCustom, EditTable, EditTableColumns } from '../../../components/editTable';
import CurrencyInput from '../../../components/input/CurrencyInput';
import PercentInput from '../../../components/input/PercentInput';
import { TItemBonusByQuantity } from '../../../types/salesPolicy';
import { handleBeforeInput } from '../../../utilities/regex';
import { useSalesPolicyStore } from '../store';

const { Title, Text } = Typography;

export interface TBonusByProductQuantityProps {
  markError: (rowKey: string, fieldKey: string) => void;
  clearError: (rowKey: string, fieldKey: string) => void;
}

const BonusByProductQuantity = (props: TBonusByProductQuantityProps) => {
  const { clearError, markError } = props;
  const form = Form.useFormInstance();
  const [dataProductQuantity, setDataProductQuantity] = useState<TItemBonusByQuantity[]>([]);
  const [disabledAdd, setDisabledAdd] = useState(false);

  const { initialValue, setIsModified, disabled, isModified } = useSalesPolicyStore();
  const defaultListBonus = initialValue?.listBonus;

  const columnsSold: (ColumnTypesCustom<TItemBonusByQuantity>[number] & EditTableColumns)[] = [
    {
      title: 'Từ',
      dataIndex: 'minProductsSold',
      key: 'minProductsSold',
      editable: true,
      alwaysEditable: true,
      align: 'center',
      width: '15%',
      inputType: 'custom',
      disabled,
      dependencies: ['maxProductsSold'],
      renderEditComponent:
        (_, index) =>
        ({ onChange, save, disabled }) => {
          const prevMaxProductsSold = dataProductQuantity[index - 1]?.maxProductsSold || '';
          index !== 0 && prevMaxProductsSold ? onChange(Number(prevMaxProductsSold) + 1) : '';
          return (
            <Input
              onChange={e => onChange(e.target.value)}
              onBlur={save}
              onPressEnter={save}
              disabled={disabled || index !== 0}
              placeholder="Nhập giá trị từ"
              maxLength={13}
              onBeforeInput={handleBeforeInput}
              type="text"
            />
          );
        },
    },
    {
      title: 'Đến',
      dataIndex: 'maxProductsSold',
      key: 'maxProductsSold',
      editable: true,
      alwaysEditable: true,
      align: 'center',
      width: '15%',
      inputType: 'text',
      disabled,
      inputProps: {
        maxLength: 13,
        onBeforeInput: handleBeforeInput,
        placeholder: 'Nhập giá trị đến',
        onChange: e => {
          setDisabledAdd(e.target.value >= '9999999999999' ? true : false);
        },
      },
      dependencies: ['minProductsSold'],
      rules: (_, index) => [
        ({ getFieldValue }) => ({
          validator: (_, value) => {
            const minProductsSold = getFieldValue('minProductsSold');
            const nextMaxProductsSold = dataProductQuantity[index + 1]?.maxProductsSold || '';
            if (minProductsSold && Number(value) <= Number(minProductsSold)) {
              markError(dataProductQuantity[index].key, 'maxProductsSold');
              return Promise.reject('Giá trị "Đến" phải lớn hơn giá trị "Từ"');
            } else if (nextMaxProductsSold && Number(value) > Number(nextMaxProductsSold)) {
              markError(dataProductQuantity[index].key, 'maxProductsSold');
              return Promise.reject('Giá trị "Đến" phải nhỏ hơn giá trị "Đến" của dòng tiếp theo.');
            }
            clearError(dataProductQuantity[index].key, 'maxProductsSold');
            return Promise.resolve();
          },
        }),
      ],
    },
    {
      title: 'Tỉ lệ ghi nhận',
      dataIndex: 'bonusPercentage',
      key: 'bonusPercentage',
      alwaysEditable: true,
      editable: true,
      width: '15%',
      disabled,
      inputType: 'custom',
      renderEditComponent:
        () =>
        ({ onChange, save, disabled }) => (
          <PercentInput
            placeholder="Nhập tỉ lệ giao dịch"
            onPressEnter={save}
            onBlur={save}
            onChange={val => onChange(val)}
            suffix="%"
            disabled={disabled}
          />
        ),
    },
    {
      title: 'Số tiền',
      dataIndex: 'bonusAmount',
      key: 'bonusAmount',
      alwaysEditable: true,
      editable: true,
      width: '15%',
      inputType: 'custom',
      disabled,
      renderEditComponent:
        () =>
        ({ onChange, save, disabled }) => (
          <CurrencyInput
            onChange={val => onChange(val || '')}
            onBlur={save}
            onPressEnter={save}
            placeholder={'Nhập số tiền phí'}
            suffix="VNĐ"
            disabled={disabled}
          />
        ),
    },
    {
      title: 'Ghi chú',
      dataIndex: 'noteProductsSold',
      key: 'noteProductsSold',
      width: '30%',
      alwaysEditable: true,
      disabled,
      editable: true,
      inputType: 'text',
      inputProps: { maxLength: 500, placeholder: 'Nhập ghi chú' },
    },
    {
      dataIndex: 'action',
      align: 'center',
      width: '10%',
      render: (_, record) => (
        <Button
          type="link"
          danger
          onClick={() => {
            handleDelete(record.key);
            setIsModified(true);
          }}
          disabled={disabled}
        >
          Xóa
        </Button>
      ),
    },
  ];

  useEffect(() => {
    if (defaultListBonus && !isModified) {
      const fieldBonus = defaultListBonus?.byQuantity;
      if (Array.isArray(fieldBonus)) {
        setDataProductQuantity(fieldBonus);
      }
    }
  }, [defaultListBonus, isModified]);

  const handleDelete = (key: React.Key) => {
    const newData = dataProductQuantity.filter(item => item.key !== key);
    setDataProductQuantity(newData);
    form.setFieldsValue({ listBonus: { byQuantity: newData } });
  };

  const handleAdd = () => {
    const prevItem = dataProductQuantity[dataProductQuantity.length - 1];
    const newMaxProductSold = prevItem?.maxProductsSold ? Number(prevItem.maxProductsSold) + 1 : '';
    const newData = {
      key: uuid(),
      minProductsSold: newMaxProductSold,
      maxProductsSold: '',
      bonusPercentage: '',
      bonusAmount: '',
      noteProductsSold: '',
    };
    setDataProductQuantity([...dataProductQuantity, newData]);
  };

  const handleSave = (row: TItemBonusByQuantity) => {
    const newData = [...dataProductQuantity];
    const index = newData.findIndex((item: TItemBonusByQuantity) => row.key === item.key);
    const item = newData[index];
    newData.splice(index, 1, { ...item, ...row });
    setDataProductQuantity(newData);
    form.setFieldsValue({
      listBonus: {
        byQuantity: newData,
      },
    });
    setIsModified(true);
  };

  return (
    <Space direction="vertical">
      <Title level={5} style={{ margin: 0 }}>
        Thưởng vượt chỉ tiêu
      </Title>
      <Flex align="baseline" justify="space-between">
        <Space align="baseline" size={'large'}>
          <Text>Thưởng theo số lượng sản phẩm</Text>
          <Form.Item name={['listBonus', 'isProgressiveByQuantity']} valuePropName="checked">
            <Checkbox disabled={disabled}>Tính lũy tiến</Checkbox>
          </Form.Item>
        </Space>
        <Button type="default" icon={<PlusOutlined />} disabled={disabledAdd || disabled} onClick={handleAdd}>
          Thêm thưởng SLSP
        </Button>
      </Flex>

      <EditTable<TItemBonusByQuantity>
        columns={columnsSold}
        dataSource={dataProductQuantity}
        handleAdd={handleAdd}
        handleDelete={handleDelete}
        handleSave={handleSave}
      />
    </Space>
  );
};

export default BonusByProductQuantity;
