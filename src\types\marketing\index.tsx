import { Dayjs } from 'dayjs';
import { TCreatedBy } from '../common/common';
import { RcFile } from 'antd/es/upload';

export interface IMarketing {
  id?: string;
  code?: string;
  name?: string;
  startDate?: Dayjs;
  endDate?: Dayjs;
  createdDate?: string;
  createdBy?: TCreatedBy;
  updatedDate?: string;
  updatedBy?: TCreatedBy;
  attachments?: TAttachment[];
  project?: string;
  expense?: string;
  costCenter?: string;
  projectCode?: string;
  projectName?: string;
  expenseCode?: string;
  expenseName?: string;
  costCenterCode?: string;
  costCenterName?: string;
  amount?: string;
  eapUrl?: string;
  eapMarketingCode?: string;
}

export type TAttachment = {
  name?: string;
  file?: RcFile;
  url?: string;
  id?: string;
  uid?: string;
  fileName?: string;
  fileUrl?: string;
};

export type TCreateMarketing = {
  id?: string;
  code?: string;
  name?: string;
  project?: string;
  expense?: string;
  costCenter?: string;
  projectCode?: string;
  projectName?: string;
  expenseCode?: string;
  expenseName?: string;
  costCenterCode?: string;
  costCenterName?: string;
  amount?: number;
  attachments?: string[];
  startDate?: string;
};

export type TUpdateMarketing = {
  id?: string;
  code?: string;
  name?: string;
  project?: string;
  expense?: string;
  costCenter?: string;
  projectCode?: string;
  projectName?: string;
  expenseCode?: string;
  expenseName?: string;
  costCenterCode?: string;
  costCenterName?: string;
  amount?: number;
  attachments?: string[];
};

export type TCostItem = {
  id: string;
  code: string;
  name: string;
  budgetCode: string;
  budgetCostName: string;
};

export type TCostCenter = {
  id: string;
  code: string;
  name: string;
  fullName: string;
};

export type ActiveTime = {
  activeTime: number;
  type: string;
};

export type TBusinessPartner = {
  id: string;
  code?: string;
  name?: string;
  email?: string;
  phone?: string;
  merchantLimitAmount?: number;
  amount?: number;
  pos?: TPos;
};

export type TPos = {
  code: string;
  name: string;
  value: string;
  label: string;
  id: string;
};

export type TSelectDropdown = {
  code: string;
  name: string;
  value: string;
  label: string;
  id: string;
  option: TBusinessPartner;
};

export type TEmployee = {
  id: string;
  name?: string;
  email?: string;
  phone?: string;
  pos?: TPos;
};
