import { Form, Input, Select, But<PERSON>, Row, Col, Typography } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { getListProject, getListProjectSaleProgram } from '../../../service/project';
import SingleSelectLazy from '../../../components/select/singleSelectLazy';
import { OptionTypeSelect } from '../../../types/common/common';
import './styles.scss';
import { FormInstance } from 'antd/lib';
import {
  EPropertyAttributeStatus,
  IPropertyAttribute,
  IPropertyAttributeFormValues,
} from '../../../types/propertyAttribute';
import { useWatch } from 'antd/es/form/Form';
import { useMemo } from 'react';

const { Title } = Typography;

const statusOptions = [
  { label: 'Hoạt động', value: EPropertyAttributeStatus.Enabled },
  { label: 'Vô hiệu', value: EPropertyAttributeStatus.Disabled },
];

interface PropertyAttributeFormProps {
  form: FormInstance<IPropertyAttributeFormValues>;
  onFinish: (values: IPropertyAttributeFormValues) => void;
  initialValues?: IPropertyAttribute;
}

export default function PropertyAttributeForm({
  form,
  onFinish,
  initialValues: initialValuesProp,
}: PropertyAttributeFormProps) {
  const projectId = useWatch('projectId', form);

  const defaultProjectValue = useMemo(() => {
    if (initialValuesProp?.project) {
      return {
        label: initialValuesProp.project.name,
        value: initialValuesProp.project.id,
      };
    }
    return undefined;
  }, [initialValuesProp?.project]);

  const defaultSaleProgramValue = useMemo(() => {
    if (initialValuesProp?.saleProgram) {
      return {
        label: initialValuesProp.saleProgram.name,
        value: initialValuesProp.saleProgram.id,
      };
    }
    return undefined;
  }, [initialValuesProp?.saleProgram]);

  const initialValues = {
    name: '',
    code: '',
    projectId: '',
    saleProgramId: '',
    status: EPropertyAttributeStatus.Enabled,
    allowedValues: [],
  };

  return (
    <Form<IPropertyAttributeFormValues>
      form={form}
      layout="vertical"
      onFinish={onFinish}
      initialValues={initialValuesProp ?? initialValues}
    >
      <Row gutter={32}>
        <Col span={12}>
          <Title level={5} className="property-attribute-modal__info-title">
            Thông tin thuộc tính
          </Title>
          <Form.Item
            label="Tên thuộc tính"
            name="name"
            rules={[{ required: true, message: 'Vui lòng nhập tên thuộc tính' }]}
          >
            <Input placeholder="Nhập tên thuộc tính" maxLength={50} />
          </Form.Item>
          <Form.Item label="Mã thuộc tính" name="code">
            <Input disabled />
          </Form.Item>
          <Form.Item
            label="Dự án áp dụng"
            name="projectId"
            rules={[{ required: true, message: 'Vui lòng chọn dự án áp dụng' }]}
          >
            <SingleSelectLazy
              apiQuery={getListProject}
              queryKey={['project']}
              keysLabel={['name']}
              handleSelect={(value: OptionTypeSelect) => {
                form.setFieldsValue({ projectId: value?.value as string });
                form.resetFields(['salesProgram']);
              }}
              placeholder="Chọn dự án"
              defaultValues={defaultProjectValue}
            />
          </Form.Item>
          <Form.Item
            label="Chương trình bán hàng"
            name="saleProgramId"
            rules={[{ required: true, message: 'Vui lòng chọn chương trình bán hàng' }]}
          >
            <SingleSelectLazy
              moreParams={{ projectId }}
              disabled={!projectId}
              apiQuery={getListProjectSaleProgram}
              queryKey={['project-sale-program', projectId]}
              keysLabel={'name'}
              handleSelect={(value: OptionTypeSelect) => {
                form.setFieldsValue({ saleProgramId: value?.value as string });
              }}
              placeholder="Chọn chương trình bán hàng"
              defaultValues={defaultSaleProgramValue}
            />
          </Form.Item>
          <Form.Item label="Trạng thái" name="status">
            <Select options={statusOptions} placeholder="Chọn trạng thái" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Title level={5} className="property-attribute-modal__value-title">
            Danh sách giá trị
          </Title>
          <Form.List name="allowedValues">
            {(fields, { add, remove }) => (
              <>
                <Button onClick={() => add()} icon={<PlusOutlined />} style={{ marginBottom: 16 }}>
                  Thêm giá trị
                </Button>
                <div className="property-attribute-modal__value-list">
                  <div className="property-attribute-modal__value-header">
                    <div className="property-attribute-modal__value-header-name">Tên</div>
                    <div className="property-attribute-modal__value-header-action"></div>
                  </div>
                </div>
                {fields.map(({ key, name, ...restField }) => (
                  <div key={key} className="property-attribute-modal__value-row">
                    <Form.Item
                      {...restField}
                      name={[name]}
                      rules={[{ required: true, message: 'Nhập tên giá trị' }]}
                      className="property-attribute-modal__value-input"
                      style={{ marginBottom: 0 }}
                    >
                      <Input placeholder="Nhập giá trị" maxLength={255} />
                    </Form.Item>
                    <div className="property-attribute-modal__value-action">
                      <Button type="link" danger onClick={() => remove(name)}>
                        Xoá
                      </Button>
                    </div>
                  </div>
                ))}
              </>
            )}
          </Form.List>
        </Col>
      </Row>
    </Form>
  );
}
