import { <PERSON><PERSON>, Col, DatePicker, Form, Input, Modal, Row, Select, Typography } from 'antd';
import { useEffect, useState } from 'react';
import { useCreateField, useFetch } from '../../../hooks';
import './styles.scss';
import React from 'react';
import ModalComponent from '../../../components/modal';
import { QueryKey } from '@tanstack/react-query';
import { FORMAT_DATE_API, OPTIONS_MARKETING_TYPE } from '../../../constants/common';
import TextArea from 'antd/es/input/TextArea';
import {
  getListCostCenter,
  getListCostItem,
  getListProject,
  uploadFile,
  createMarketing,
} from '../../../service/marketing';

import { TProjectDropdown } from '../../../types/common/common';
import { IMarketing, TCostCenter, TCostItem, TCreateMarketing } from '../../../types/marketing';
import dayjs from 'dayjs';
import { handleKeyDownEnterNumber } from '../../../utilities/regex';
import UploadAttachments from '../component/uploadAttachments';

const { Item } = Form;
const { Title } = Typography;
const { RangePicker } = DatePicker;

interface CreateModalMarketingProps {
  visible: boolean;
  onClose: () => void;
  keyQuery: QueryKey;
}

const CreateModalMarketing = ({ visible, onClose, keyQuery }: CreateModalMarketingProps) => {
  const [form] = Form.useForm();

  const [isModified, setIsModified] = useState(false);

  const muateCreateMarketing = useCreateField<TCreateMarketing>({
    keyOfDetailQuery: keyQuery,
    keyOfListQuery: ['get-list-marketing'],
    apiQuery: createMarketing,
    label: 'kế hoạch kinh doanh tiếp thị',
  });

  const { data: dataProjects } = useFetch<TProjectDropdown[]>({
    queryKeyArrWithFilter: ['get-projects'],
    api: getListProject,
  });

  const { data: dataCostCenters } = useFetch<TCostCenter[]>({
    queryKeyArrWithFilter: ['get-cost-centers'],
    api: getListCostCenter,
  });

  const { data: dataCostItems } = useFetch<TCostItem[]>({
    queryKeyArrWithFilter: ['get-cost-items'],
    api: getListCostItem,
  });

  // cảnh báo khi thoát trang khi chưa lưu dữ liệu
  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  const handleCancel = React.useCallback(() => {
    if (form.isFieldsTouched()) {
      Modal.confirm({
        title: 'Xác nhận hủy',
        content: 'Dữ liệu đang chưa được lưu, bạn có chắc muốn hủy dữ liệu đang nhập không?',
        cancelText: 'Quay lại',
        onOk: () => {
          form.resetFields();
          onClose();
        },
        okButtonProps: {
          type: 'default',
        },
        cancelButtonProps: {
          type: 'primary',
        },
      });
      setIsModified(false);
    } else {
      form.resetFields();
      setIsModified(false);
      onClose();
    }
  }, [form, onClose]);

  const handleFinish = async (values: IMarketing) => {
    const project = dataProjects?.data?.data?.rows?.find(item => item?.code === values?.project);
    const costCenter = dataCostCenters?.data?.data?.rows?.find(item => item?.code === values?.costCenter);
    const costItem = dataCostItems?.data?.data?.rows?.find(item => item?.budgetCode === values?.expense);

    const startDate = form?.getFieldValue('startDate');
    const endDate = form?.getFieldValue('endDate');

    const transformedValues = {
      ...values,
      projectCode: project?.code,
      projectName: project?.name,
      costCenterCode: costCenter?.code,
      costCenterName: costCenter?.fullName,
      expenseCode: costItem?.budgetCode,
      expenseName: costItem?.budgetCostName,
      expense: undefined,
      project: undefined,
      costCenter: undefined,
      amount: Number(values?.amount),
      attachments: values?.attachments?.map(item => item?.fileUrl as string),
      startDate: dayjs(startDate)?.format(FORMAT_DATE_API),
      endDate: dayjs(endDate)?.format(FORMAT_DATE_API),
    };

    const res = await muateCreateMarketing.mutateAsync(transformedValues);
    const statusCode = res?.data?.statusCode;
    if (statusCode === '0') {
      form.resetFields();
      setIsModified(false);
      onClose();
    }
  };

  const validateForm = () => {
    setIsModified(true);
  };

  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  return (
    <>
      <ModalComponent
        title="Tạo mới kế hoạch kinh doanh tiếp thị"
        open={visible}
        onCancel={handleCancel}
        destroyOnClose
        footer={
          <>
            <Button type="primary" htmlType="submit" onClick={() => form.submit()}>
              Lưu
            </Button>
          </>
        }
      >
        <Form form={form} layout="vertical" onFinish={handleFinish} onValuesChange={validateForm} className="space-y-6">
          <Row gutter={{ md: 24, lg: 40, xl: 80, xxl: 126 }}>
            <Col xs={24} md={12}>
              <Title level={5}>Thông tin chung</Title>
              <Row gutter={{ md: 24, lg: 40 }}>
                <Col xs={24} md={12}>
                  <Item
                    label="Tên kế hoạch"
                    name="marketingName"
                    required
                    rules={[
                      {
                        required: true,
                        validator: (_, value) => {
                          if (!value || value.trim() === '') {
                            return Promise.reject(new Error('Vui lòng nhập tên kế hoạch'));
                          }
                          return Promise.resolve();
                        },
                      },
                    ]}
                  >
                    <Input placeholder="Nhập tên kế hoạch" maxLength={255} />
                  </Item>
                </Col>
                <Col xs={24} md={12}>
                  <Item
                    label="Loại chủ trương"
                    name="marketingType"
                    required
                    rules={[{ required: true, message: 'Vui lòng chọn loại chủ trương' }]}
                  >
                    <Select placeholder="Chọn loại chủ trương" allowClear options={OPTIONS_MARKETING_TYPE} />
                  </Item>
                </Col>
                <Col xs={24} md={24}>
                  <Item
                    label="Mã dự án"
                    name="project"
                    required
                    rules={[{ required: true, message: 'Vui lòng chọn loại tờ trình' }]}
                  >
                    <Select
                      placeholder="Chọn mã dự án"
                      allowClear
                      options={dataProjects?.data?.data?.rows?.map((item: TProjectDropdown) => ({
                        key: item?.code,
                        label: item?.name,
                        value: item?.code,
                      }))}
                    />
                  </Item>
                </Col>
                <Col xs={24} md={24}>
                  <Item
                    label="Số tiền đề xuất dự kiến"
                    name="amount"
                    required
                    rules={[
                      {
                        required: true,
                        validator: (_, value) => {
                          if (!value || value.trim() === '') {
                            return Promise.reject(new Error('Vui lòng nhập số tiền'));
                          }
                          return Promise.resolve();
                        },
                      },
                    ]}
                  >
                    <Input
                      onKeyDown={handleKeyDownEnterNumber}
                      placeholder="Nhập số tiền"
                      maxLength={255}
                      suffix={'VNĐ'}
                    />
                  </Item>
                </Col>
                <Col xs={24} md={24}>
                  <Item
                    label="Mã ngân sách"
                    name="costCenter"
                    required
                    rules={[{ required: true, message: 'Vui lòng chọn mã ngân sách' }]}
                  >
                    <Select
                      placeholder="Chọn mã ngân sách"
                      allowClear
                      options={dataCostCenters?.data?.data?.rows?.map((item: TProjectDropdown) => ({
                        key: item?.code,
                        label: item?.name,
                        value: item?.code,
                      }))}
                    />
                  </Item>
                </Col>
                <Col xs={24} md={24}>
                  <Item
                    label="Mã chi phí"
                    name="expense"
                    required
                    rules={[{ required: true, message: 'Vui lòng chọn mã chi phí' }]}
                  >
                    <Select
                      placeholder="Chọn mã chi phí"
                      allowClear
                      options={dataCostItems?.data?.data?.rows?.map((item: TCostItem) => ({
                        key: item?.id,
                        label: item?.name,
                        value: item?.budgetCode,
                      }))}
                    />
                  </Item>
                </Col>
                <Col xs={24} md={24}>
                  <Form.Item name={'time'} label="Thời gian">
                    <RangePicker
                      // value={[form?.getFieldValue('startDate'), form?.getFieldValue('endDate')]}
                      format="DD/MM/YYYY"
                      onChange={dates => {
                        form.setFieldsValue({
                          startDate: dates?.[0],
                          endDate: dates?.[1],
                        });
                        setIsModified(true);
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} md={24}>
                  <Item
                    label="Nội dung kế hoạch"
                    name="content"
                    required
                    rules={[{ required: true, message: 'Vui lòng nhập nội dung' }]}
                  >
                    <TextArea placeholder="Nhập nội dung" />
                  </Item>
                </Col>
              </Row>
            </Col>
            <Col xs={24} md={12}>
              <Title level={5}>File đính kèm</Title>
              <Row gutter={{ md: 24, lg: 40 }}>
                <Col xs={24} md={24}>
                  <UploadAttachments apiUpload={uploadFile} fieldAttachments="attachments" />
                </Col>
              </Row>
            </Col>
          </Row>
        </Form>
      </ModalComponent>
    </>
  );
};

export default CreateModalMarketing;
