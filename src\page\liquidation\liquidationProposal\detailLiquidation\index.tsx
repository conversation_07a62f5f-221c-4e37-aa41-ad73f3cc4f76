import React from 'react';
import { But<PERSON>, Form, Typography } from 'antd';
import { useCreateField, useFetch, useUpdateField } from '../../../../hooks';
import BreadCrumbComponent from '../../../../components/breadCrumb';
import { Link, useNavigate, useParams } from 'react-router-dom';
import { ILiquidation, LiquidationFormValues } from '../../../../types/liquidation';
import {
  approveLiquidationProposal,
  getDetailLiquidationProposal,
  requestApproveLiquidationProposal,
  updateLiquidationProposal,
} from '../../../../service/liquidation';
import FPTLogo from '../../../../assets/images/Default_Logo_Project.png';
import { LIQUIDATION_PROPOSAL_MANAGEMENT, PROJECTS_MANAGEMENT } from '../../../../configs/path';
import StatusTag from '../../../contractManagement/components/StatusTag';
import LiquidationProposalForm from '../components/formLiquidation';

interface LiquidationDetailProps {
  initialData?: ILiquidation;
}
const { Title, Text } = Typography;

const LiquidationProposalDetail: React.FC<LiquidationDetailProps> = ({ initialData }) => {
  const { id } = useParams<{ id: string }>();
  const [form] = Form.useForm();
  const navigate = useNavigate();

  const { data, isLoading, isFetching } = useFetch<ILiquidation>({
    queryKeyArr: ['get-detail-liquidation-proposal'],
    api: () => id && getDetailLiquidationProposal(id || ''),
    enabled: !!id,
    cacheTime: 10,
  });
  const liquidationProposal = data?.data?.data || initialData;

  const { mutateAsync: update, isPending: isUpdateLiquidationProposal } = useUpdateField<LiquidationFormValues>({
    keyOfListQuery: ['get-list-liquidation-proposal'],
    keyOfDetailQuery: ['get-detail-liquidation-proposal'],
    apiQuery: updateLiquidationProposal,
    isMessageError: false,
    messageSuccess: 'Chỉnh sửa đề nghị thanh lý thành công!',
  });

  const { mutateAsync: requestApprove, isPending: isPendingApproveLiquidation } = useCreateField<{
    id: string;
    status: string;
  }>({
    keyOfListQuery: ['get-list-liquidation-proposal'],
    keyOfDetailQuery: ['get-detail-liquidation-proposal'],
    apiQuery: requestApproveLiquidationProposal,
    isMessageError: false,
    messageSuccess: 'Trình duyệt đề nghị thanh lý thành công!',
  });

  const { mutateAsync: approve, isPending: isPendingApprove } = useCreateField<{ id: string; status: string }>({
    keyOfListQuery: ['get-list-liquidation-proposal'],
    keyOfDetailQuery: ['get-detail-liquidation-proposal'],
    apiQuery: approveLiquidationProposal,
    isMessageError: false,
    messageSuccess: 'Duyệt đề nghị thanh lý thành công!',
  });
  const { mutateAsync: reject, isPending: isPendingReject } = useCreateField<{ id: string; status: string }>({
    keyOfListQuery: ['get-list-liquidation-proposal'],
    keyOfDetailQuery: ['get-detail-liquidation-proposal'],
    apiQuery: approveLiquidationProposal,
    isMessageError: false,
    messageSuccess: 'Từ chối đề nghị thanh lý thành công!',
  });

  const handleSubmitForApproval = async () => {
    if (liquidationProposal?.id) {
      const resp = await requestApprove({ id: liquidationProposal.id, status: 'waiting' });
      if (resp?.data?.statusCode === '0' && resp?.data?.success) {
        await form.resetFields();
      }
    }
  };
  const canSubmitForApproval = liquidationProposal?.status === 'init' || liquidationProposal?.status === 'rejected';

  const handleFinish = async (values: LiquidationFormValues) => {
    const resp = await update({ ...values, id: liquidationProposal?.id });
    if (resp?.data?.statusCode === '0' && resp?.data?.success) {
      await form.resetFields();
      navigate(LIQUIDATION_PROPOSAL_MANAGEMENT);
    }
  };
  const canApprove = liquidationProposal?.status === 'waiting';
  const handleApprove = async () => {
    if (liquidationProposal?.id) {
      const resp = await approve({ id: liquidationProposal.id, status: 'approved' });
      if (resp?.data?.statusCode === '0' && resp?.data?.success) {
        await form.resetFields();
        navigate(LIQUIDATION_PROPOSAL_MANAGEMENT);
      }
    }
  };
  const handleReject = async () => {
    if (liquidationProposal?.id) {
      const resp = await reject({ id: liquidationProposal.id, status: 'rejected' });
      if (resp?.data?.statusCode === '0' && resp?.data?.success) {
        await form.resetFields();
        navigate(LIQUIDATION_PROPOSAL_MANAGEMENT);
      }
    }
  };

  return (
    <div className="discount-policy-detail">
      <BreadCrumbComponent titleBread={data?.data?.data?.name} />
      <div className="project-card">
        <div className="project-info">
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <Title level={5} style={{ margin: 0 }}>
              {liquidationProposal?.name}
            </Title>
            <StatusTag status={liquidationProposal?.status} />
          </div>
          <Text type="secondary">
            Dự án: &nbsp;
            <Link to={`${PROJECTS_MANAGEMENT}/${liquidationProposal?.escrowTicket?.project?.id}`}>
              {liquidationProposal?.escrowTicket?.project?.name || '-'}
            </Link>
          </Text>
        </div>

        <div style={{ display: 'flex', gap: '10px' }}>
          {canSubmitForApproval && (
            <Button type="primary" onClick={handleSubmitForApproval} loading={isPendingApproveLiquidation}>
              Trình và duyệt
            </Button>
          )}
          {canApprove && (
            <Button onClick={handleApprove} loading={isPendingApprove}>
              Duyệt
            </Button>
          )}
          {canApprove && (
            <Button onClick={handleReject} loading={isPendingReject}>
              Từ chối
            </Button>
          )}
          <Button>In hợp đồng</Button>
        </div>
        <div className="project-image">
          <img
            src={
              liquidationProposal?.escrowTicket?.project?.imageUrl
                ? `${import.meta.env.VITE_S3_IMAGE_URL}/${liquidationProposal?.escrowTicket?.project?.imageUrl}`
                : FPTLogo
            }
            alt="Project"
          />
        </div>
      </div>
      <LiquidationProposalForm
        form={form}
        isUpdate={true}
        onFinish={handleFinish}
        initialValues={liquidationProposal}
        loading={isLoading || isFetching || isUpdateLiquidationProposal}
      />
    </div>
  );
};

export default LiquidationProposalDetail;
