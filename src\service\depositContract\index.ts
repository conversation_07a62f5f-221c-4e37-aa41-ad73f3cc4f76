import { deleteRequest, getRequest, postRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import { EscrowContract } from '../../types/depositContract';

export const getListDepositContract = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/deposit-contract`,
    params as Record<string, unknown> | undefined,
  );
};

export const getDetailDepositContract = async (escrowContractId: string) => {
  return await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/deposit-contract/${escrowContractId}`,
  );
};

export const deleteDepositContract = async (payload: { id: string; reason: string }) => {
  const { id, reason } = payload;
  return await deleteRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/deposit-contract/${id}`,
    { reason },
  );
};

export const updatePaymentTracking = async (payload: { id: string; installmentId: string }) => {
  return await putRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/deposit-contract/installment/transactionSuccessful`,
    payload,
  );
};

export const updateDepositContract = async (payload: EscrowContract) => {
  return await putRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/deposit-contract/${payload?.id}`,
    payload,
  );
};

export const createDepositContract = async (payload: unknown) => {
  return await postRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/deposit-contract`,
    payload,
  );
};

export const getListOrgchartInternal = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_orgChart}/${typeQueryVersionApi.query_v2}/orgchart/allCompanyByQuery`,
    params as Record<string, unknown> | undefined,
  );
};

export const getListOrgchartExternal = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_orgChart}/${typeQueryVersionApi.query}/external/orgchart/findAllByQuery`,
    params as Record<string, unknown> | undefined,
  );
};

export const getListSalesPolicy = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.query}/sales-policy/drop-down`,
    params as Record<string, unknown> | undefined,
  );
};

export const getListDistributionChannel = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_masterdata_producer}/${typeQueryVersionApi.api_v1}/division/get-all`,
    params as Record<string, unknown> | undefined,
  );
};

export const getListDivision = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_masterdata_producer}/${typeQueryVersionApi.api_v1}/distribution-channel/get-all`,
    params as Record<string, unknown> | undefined,
  );
};

export const getListOrgchartPartner = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_orgChart}/${typeQueryVersionApi.query_v2}/orgchart/allCompanyByQuery`,
    params as Record<string, unknown> | undefined,
  );
};

export const getListSaleProgram = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/salesProgram`,
    params as Record<string, unknown> | undefined,
  );
};

export const getBlockBySaleProgramId = async (params: unknown) => {
  const { idSaleProgram, ...restParams } = params as Record<string, unknown>;
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/propertyUnit/getBlockBySaleProgramId/${idSaleProgram}`,
    restParams as Record<string, unknown> | undefined,
  );
  return response;
};

export const getBlockBySalesProgramIds = async (params: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/propertyUnit/getBlockBySaleProgramIds`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const getFloorAndRoomByBlockId = async (params: unknown) => {
  const { saleProgramId, ...restParams } = params as Record<string, unknown>;
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/propertyUnit/getFloorBySaleProgramId/${saleProgramId}`,
    restParams as Record<string, unknown> | undefined,
  );
  return response;
};

export const getListDepositProject = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/project`,
    params as Record<string, unknown> | undefined,
  );
};

export const getListEscrowProduct = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/propertyUnit/getPropertyUnitsByQuery`,
    params as Record<string, unknown> | undefined,
  );
};
