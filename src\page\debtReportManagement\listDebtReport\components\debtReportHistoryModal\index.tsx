import { Button, Col, Form, FormInstance, Input, Modal, Row, Space, Timeline, Typography } from 'antd';
import { memo, useMemo, useState } from 'react';
import TextArea from 'antd/es/input/TextArea';
import dayjs from 'dayjs';

import './styles.scss';

import { IDebReport, THistory, TNote } from '../../../../../types/debtReport';
import { DEBT_REPORT_STATUS, FORMAT_DATE_TIME, OPTIONS_GENDER_V2 } from '../../../../../constants/common';
import { updateAddNote, updateCallRecord } from '../../../../../service/debtReport';
import { useUpdateField } from '../../../../../hooks';

const { Text } = Typography;

type Props = {
  isOpen: boolean;
  record: IDebReport;
  handleCancel: () => void;
  queryKeyArr?: string[];
  keyOfDetailQuery?: string[];
};

const ModalHistoryDebtReport = ({ isOpen, record, handleCancel, queryKeyArr, keyOfDetailQuery }: Props) => {
  const [formNote] = Form.useForm();
  const [formCall] = Form.useForm();
  const [isOpenCallReport, setIsOpenCallReport] = useState(false);
  const [isOpenNote, setIsOpenNote] = useState(false);

  const toggleModal = (type: 'call' | 'note') => {
    type === 'call' ? setIsOpenCallReport(!isOpenCallReport) : setIsOpenNote(!isOpenNote);
  };

  const { mutateAsync: updateNote } = useUpdateField({
    apiQuery: updateAddNote,
    keyOfListQuery: queryKeyArr,
    keyOfDetailQuery,
  });

  const { mutateAsync: updateCall } = useUpdateField({
    apiQuery: updateCallRecord,
    keyOfListQuery: queryKeyArr,
    keyOfDetailQuery,
  });

  const handleSubmitForm = async (values: TNote, type: 'note' | 'call') => {
    const apiFunc = type === 'note' ? updateNote : updateCall;
    const form = type === 'note' ? formNote : formCall;

    const response = await apiFunc({ ...values, id: record?.id });
    if (response?.data?.statusCode === '0') {
      form?.resetFields();
      toggleModal(type);
    }
  };

  const statusCustomer = useMemo(() => {
    return DEBT_REPORT_STATUS[record?.debtHistory?.status as keyof typeof DEBT_REPORT_STATUS];
  }, [record?.debtHistory?.status]);

  const genderLabel = useMemo(() => {
    return (
      OPTIONS_GENDER_V2?.find(
        item => item?.value?.toLowerCase() === record?.debtHistory?.customer?.gender?.toLowerCase(),
      )?.label ?? '--'
    );
  }, [record?.debtHistory?.customer?.gender]);

  const timelineItems = record?.debtHistory?.histories
    ?.map((item: THistory) => {
      const status = DEBT_REPORT_STATUS[item?.status as keyof typeof DEBT_REPORT_STATUS];

      return {
        color: status?.color,
        children: (
          <div>
            <div className="text-history">
              <Text>{status?.label}</Text>
              <Text type="secondary">{dayjs(item?.createdDate).format(FORMAT_DATE_TIME)}</Text>
            </div>

            <div className="text-history">
              <Text>Nhân viên thực hiện: </Text>
              <Text>
                {item?.createdBy?.username} - {item?.createdBy?.fullName}
              </Text>
            </div>
          </div>
        ),
      };
    })
    ?.reverse();

  const renderFormModal = (
    type: 'note' | 'call',
    form: FormInstance,
    isOpen: boolean,
    title: string,
    onSubmit: () => void,
  ) => (
    <Modal
      className="modal-share-care"
      open={isOpen}
      title={title}
      width={380}
      onCancel={() => {
        form?.resetFields();
        toggleModal(type);
      }}
      zIndex={1100}
      maskClosable={false}
      footer={[
        <Button key="submit" type="primary" onClick={onSubmit}>
          Xác nhận
        </Button>,
        <Button key="cancel" onClick={() => toggleModal(type)}>
          Hủy
        </Button>,
      ]}
    >
      <Form form={form} onFinish={values => handleSubmitForm(values, type)} layout="vertical">
        <Form.Item label="Tiêu đề" name="title" required rules={[{ required: true, message: 'Vui lòng nhập tiêu đề' }]}>
          <Input placeholder={`Nhập tiêu đề ${type === 'note' ? 'ghi chú' : 'cuộc gọi'}`} />
        </Form.Item>
        <Form.Item
          label="Nội dung"
          name="detail"
          required
          rules={[{ required: true, message: 'Vui lòng nhập nội dung' }]}
        >
          <TextArea placeholder={`Nhập nội dung ${type === 'note' ? 'ghi chú' : 'cuộc gọi'}`} />
        </Form.Item>
      </Form>
    </Modal>
  );

  return (
    <>
      <Modal
        className="product-detail-modal"
        open={isOpen}
        title={`Hợp đồng ${record?.contractName}`}
        onCancel={handleCancel}
        footer={null}
        width={1026}
      >
        <Row gutter={24} className="content-history-debt-report">
          <Col lg={12}>
            <Row gutter={24}>
              <Col lg={24}>
                <Space size={16}>
                  <Text className="customer-name">{record?.debtHistory?.customer?.name}</Text>
                  <Text
                    style={{
                      fontSize: 12,
                      color: ['note', 'call'].includes(record?.debtHistory?.status ?? '')
                        ? '#FAAD14'
                        : statusCustomer?.color,
                    }}
                  >
                    {['note', 'call'].includes(record?.debtHistory?.status ?? '')
                      ? 'Đang tương tác'
                      : statusCustomer?.label}
                  </Text>
                </Space>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col lg={24}>
                <Space>
                  <Button onClick={() => toggleModal('call')}>Ghi nhận cuộc gọi</Button>
                  <Button onClick={() => toggleModal('note')}>Ghi chú</Button>
                </Space>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col lg={24}>
                <Space direction="vertical" size={16}>
                  <Text>
                    <strong>Giới tính: </strong>
                    {genderLabel}
                  </Text>
                  <Text>
                    <strong>Số điện thoại:</strong> {record?.debtHistory?.customer?.phone}
                  </Text>
                  <Text>
                    <strong>Địa chỉ:</strong> {record?.debtHistory?.customer?.address}
                  </Text>
                </Space>
              </Col>
            </Row>
          </Col>

          <Col lg={12}>
            <div>
              <Text className="interaction-history-title">Lịch sử tương tác</Text>
              <Timeline reverse mode="left" items={timelineItems} />
            </div>
          </Col>
        </Row>
      </Modal>

      {renderFormModal('call', formCall, isOpenCallReport, 'Ghi nhận cuộc gọi', () => formCall.submit())}
      {renderFormModal('note', formNote, isOpenNote, 'Ghi chú', () => formNote.submit())}
    </>
  );
};

export default memo(ModalHistoryDebtReport);
