import { usePara<PERSON>, useNavigate, useLocation } from 'react-router-dom';
import { useEffect } from 'react';
import BreadCrumbComponent from '../../../components/breadCrumb';
import { useFetch } from '../../../hooks';
import { getDetailOffer } from '../../../service/offer';
import { DetailOfferMoneyAccountancy } from '../../../types/offer';
import { Button, Col, Row, Spin, TableColumnsType, Typography } from 'antd';
import './styles.scss';
import TableComponent from '../../../components/table';
import { formatCurrency, getFullAddress } from '../../../utilities/shareFunc';
import dayjs from 'dayjs';
import { FORMAT_DATE, FORMAT_DATE_TIME, PAYMENT_METHOD_NAME, STATUS_LABELS } from '../../../constants/common';
import { OFFER_ORDER_MONEY_ACCOUNTANCY_MANAGEMENT } from '../../../configs/path';
import { formatNumber } from '../../../utilities/regex';
import { PaperClipOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;
const ReceiptAutoDetail: React.FC<{}> = ({}) => {
  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();

  const { data: offerOrderData, isRefetching } = useFetch<DetailOfferMoneyAccountancy>({
    api: () => id && getDetailOffer({ id }),
    queryKeyArr: ['get-detail-offer', id],
    enabled: !!id,
    cacheTime: 10,
  });

  const offerOrder = offerOrderData?.data?.data;

  const handleNavigateToDetail = (recordId: string) => {
    // Store current scroll position
    const currentScrollY = window.scrollY;

    // Navigate to new record
    navigate(`${OFFER_ORDER_MONEY_ACCOUNTANCY_MANAGEMENT}/auto/${recordId}`, {
      replace: true,
      state: {
        scrollY: currentScrollY,
        preserveScroll: true,
      },
    });
  };

  // Restore scroll position after navigation and data loads
  useEffect(() => {
    const state = location.state as { scrollY?: number; preserveScroll?: boolean };

    if (state?.preserveScroll && state?.scrollY !== undefined && !isRefetching) {
      // Use requestAnimationFrame for better timing
      requestAnimationFrame(() => {
        setTimeout(() => {
          window.scrollTo({
            top: state.scrollY,
            behavior: 'instant',
          });
        }, 50);
      });
    }
  }, [isRefetching, location.state, offerOrder]);

  const columns: TableColumnsType<DetailOfferMoneyAccountancy> = [
    {
      title: 'Dự án',
      dataIndex: 'projectName',
      key: 'projectName',
      width: 107,
      render: (_: string, record: DetailOfferMoneyAccountancy) => (
        <Text>{record?.propertyTicket?.project?.name || ''}</Text>
      ),
    },
    {
      title: 'Mã YCĐCHO/ YCĐCO',
      dataIndex: 'bookingTicketCode',
      key: 'bookingTicketCode',
      width: 160,
      render: (_: string, record: DetailOfferMoneyAccountancy) => (
        <Text>
          {record?.propertyTicket?.ticketType === 'YCDCH'
            ? record?.propertyTicket?.bookingTicketCode
            : record?.propertyTicket?.escrowTicketCode || ''}
        </Text>
      ),
    },

    {
      title: 'Số sản phẩm',
      dataIndex: 'propertyUnitCode',
      key: 'propertyUnitCode',
      width: 140,
      render: (_: string, record: DetailOfferMoneyAccountancy) => (
        <Text>{record?.propertyTicket?.propertyUnit?.code || ''}</Text>
      ),
    },
    {
      title: 'Mã đề nghị',
      dataIndex: 'code',
      key: 'code',
      width: 110,
      render: (value: string, record?: DetailOfferMoneyAccountancy) => (
        <Button
          type="link"
          onClick={e => {
            e.preventDefault();
            e.stopPropagation();
            if (record?.id) {
              handleNavigateToDetail(record.id);
            }
          }}
        >
          {value || ''}
        </Button>
      ),
    },

    {
      title: 'Số tiền',
      dataIndex: 'money',
      key: 'money',
      width: 120,
      align: 'center',
      render: (value: number) => <Text>{formatCurrency(value?.toString()) || ''}</Text>,
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      align: 'center',
      render: (value: string) => <Text style={{ color: 'black' }}>{STATUS_LABELS[value] || ''}</Text>,
    },
    {
      title: 'Ngày cập nhật',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 160,
      render: (value: string) => <Text>{dayjs(value).format(FORMAT_DATE) || ''}</Text>,
    },
  ];

  return (
    <Spin spinning={isRefetching}>
      <div>
        <BreadCrumbComponent titleBread={offerOrder?.code} />
        <Title
          level={5}
          style={{ borderBottom: '1px solid rgba(0, 0, 0, 0.06)', padding: '20px 0px', color: 'rgba(0, 0, 0, 1)' }}
        >
          Phiếu thu {offerOrder?.code}
        </Title>

        <Title
          level={5}
          style={{ fontSize: '14px', marginBottom: '16px', marginTop: '30px', color: 'rgba(0, 0, 0, 1)' }}
        >
          Thông tin chi tiết
        </Title>
        <div className="customer-info">
          <div className="info-row">
            <span className="info-label">Mã đề nghị:</span>
            <span className="info-value">{offerOrder?.code || ''}</span>
          </div>
          <div className="info-row">
            <span className="info-label">Mã khách hàng:</span>
            <span className="info-value">{offerOrder?.propertyTicket?.customer?.code || ''}</span>
          </div>
          <div className="info-row">
            <span className="info-label">Tên khách hàng:</span>
            <span className="info-value">
              {offerOrder?.propertyTicket?.customer?.type === 'business'
                ? offerOrder?.propertyTicket?.customer?.company?.name
                : offerOrder?.propertyTicket?.customer?.personalInfo?.name || ''}
            </span>
          </div>

          {/* <div className="info-row">
          <span className="info-label">Tên công ty:</span>
          <span className="info-value">{offerOrder?.propertyTicket?.customer?.company?.name || ''}</span>
        </div> */}
          <div className="info-row">
            <span className="info-label">Số giấy tờ:</span>
            <span className="info-value">
              <span className="info-value">{offerOrder?.propertyTicket?.customer?.identityNumber || ''}</span>
            </span>
          </div>
          <div className="info-row">
            <span className="info-label">Địa chỉ liên lạc:</span>
            <span className="info-value">
              {offerOrder?.propertyTicket?.customer?.type === 'individual'
                ? getFullAddress(offerOrder?.propertyTicket?.customer?.info?.rootAddress)
                : getFullAddress(offerOrder?.propertyTicket?.customer?.company?.address) || ''}
            </span>
          </div>
          <div className="info-row">
            <span className="info-label">Số tiền:</span>
            <span className="info-value">{formatNumber(offerOrder?.money) || ''}</span>
          </div>
          <div className="info-row">
            <span className="info-label">Hình thức thanh toán:</span>
            <span className="info-value">{offerOrder?.state ? PAYMENT_METHOD_NAME[offerOrder?.state] : ''}</span>
          </div>
          <div className="info-row">
            <span className="info-label">Ngày tạo đề nghị thu tiền:</span>
            <span className="info-value">{dayjs(offerOrder?.createdAt).format(FORMAT_DATE_TIME) || ''}</span>
          </div>
          <div className="info-row">
            <span className="info-label">Ngày nộp tiền:</span>
            <span className="info-value">{dayjs(offerOrder?.receiptDate).format(FORMAT_DATE_TIME) || ''}</span>
          </div>
          <div className="info-row">
            <span className="info-label">Ghi chú:</span>
            <span className="info-value">{offerOrder?.description || ''}</span>
          </div>
          <div className="info-row">
            <span className="info-label">Ghi chú phiếu YC:</span>
            <span className="info-value">{offerOrder?.propertyTicket?.note || ''}</span>
          </div>
          <div className="info-row">
            <span className="info-label">Lí do thanh toán:</span>
            <span className="info-value">{offerOrder?.reason || ''}</span>
          </div>
          <div className="info-row">
            <span className="info-label">Phí giao dịch:</span>
            <span className="info-value">{formatNumber(offerOrder?.transactionFee) || ''}</span>
          </div>
          <div className="info-row">
            <span className="info-label">Thông tin đính kèm:</span>
            <span className="info-value">
              {Array.isArray(offerOrder?.files) ? (
                <>
                  {offerOrder?.files?.map((file, index) => (
                    <div key={index}>
                      {file?.url ? (
                        <a
                          href={`${import.meta.env.VITE_S3_IMAGE_URL}/${file?.url}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          style={{ color: '#1890ff' }}
                          download={file?.name}
                        >
                          <PaperClipOutlined />
                          {file?.name}
                        </a>
                      ) : (
                        <span style={{ color: '#1890ff' }}>{file?.name}</span>
                      )}
                    </div>
                  ))}
                </>
              ) : (
                ''
              )}
            </span>
          </div>
        </div>

        <Title
          level={5}
          style={{ fontSize: '14px', marginBottom: '16px', marginTop: '30px', color: 'rgba(0, 0, 0, 1)' }}
        >
          Thông tin chung
        </Title>

        <TableComponent
          className="table-offer-order"
          columns={columns}
          queryKeyArr={['get-offer-order']}
          dataSource={offerOrder?.relatedTransactions}
          rowKey={'id'}
          isPagination={false}
        />
        <Row
          style={{
            backgroundColor: 'rgba(230, 244, 255, 1)',
            padding: '12px 8px',
            borderBottom: '1px solid rgba(0, 0, 0, 0.06)',
            marginTop: '30px',
          }}
        >
          <Col span={8} style={{ textAlign: 'center' }}>
            <span>
              Số tiền: <span style={{ fontWeight: 600 }}>{formatNumber(offerOrder?.totalPendingAmount)}</span>
            </span>
          </Col>
          <Col span={8} style={{ textAlign: 'center' }}>
            <span>
              Đã thanh toán: <span style={{ fontWeight: 600 }}>{formatNumber(offerOrder?.totalPaidAmount)}</span>
            </span>
          </Col>
          <Col span={8} style={{ textAlign: 'right' }}>
            <span>
              Còn lại: <span style={{ fontWeight: 600 }}>{formatNumber(offerOrder?.totalRemainingAmount)}</span>
            </span>
          </Col>
        </Row>
      </div>
    </Spin>
  );
};

export default ReceiptAutoDetail;
