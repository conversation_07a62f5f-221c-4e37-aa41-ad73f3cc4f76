import { Modal, Form } from 'antd';
import { IPropertyAttributeFormValues } from '../../../types/propertyAttribute';
import './styles.scss';
import { useCreateField } from '../../../hooks';
import { createPropertyAttribute } from '../../../service/propertyAttribute';
import PropertyAttributeForm from './PropertyAttributeForm';

interface CreatePropertyAttributeModalProps {
  open: boolean;
  onCancel: () => void;
  onOk?: (values: IPropertyAttributeFormValues) => void;
}

const CreatePropertyAttributeModal = ({ open, onCancel, onOk }: CreatePropertyAttributeModalProps) => {
  const [form] = Form.useForm<IPropertyAttributeFormValues>();

  const { mutateAsync: createProperty, isPending } = useCreateField({
    apiQuery: createPropertyAttribute,
    keyOfListQuery: ['property-attribute'],
    messageSuccess: 'Tạo mới thuộc tính thành công',
  });

  const handleFinish = async (values: IPropertyAttributeFormValues) => {
    const newValues = {
      ...values,
      name: values.name.trim(),
    };

    const res = await createProperty(newValues);
    if (res?.data?.statusCode === '0') {
      form.resetFields();
      onCancel();
      onOk?.(newValues);
    }
  };

  return (
    <Modal
      wrapClassName="wrapper-modal-lead-common"
      open={open}
      width={900}
      title="Tạo mới thuộc tính"
      destroyOnClose
      okButtonProps={{ loading: isPending }}
      onOk={() => form.submit()}
      onCancel={() => {
        onCancel();
        form.resetFields();
      }}
    >
      <PropertyAttributeForm form={form} onFinish={handleFinish} />
    </Modal>
  );
};

export default CreatePropertyAttributeModal;
