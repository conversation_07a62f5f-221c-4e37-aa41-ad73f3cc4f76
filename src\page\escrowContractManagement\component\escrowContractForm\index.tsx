import { <PERSON><PERSON>, Col, Form, Modal, Row, Typography, UploadFile } from 'antd';
import React, { useCallback, useEffect, useMemo, useState } from 'react';

import { useFetch } from '../../../../hooks';
import { Bank, BankOption } from '../../../../types/bookingRequest';
import { getBanks } from '../../../../service/bank';
import dayjs from 'dayjs';
import './styles.scss';
import { EscrowContract, Installment } from '../../../../types/depositContract';
import { formatNumber, handleKeyDownEnterNumber } from '../../../../utilities/regex';
import { ORGCHART_TYPE } from '../../../../constants/common';
import { getListSalesPolicy } from '../../../../service/depositContract';
import SingleSelectLazy from '../../../../components/select/singleSelectLazy';
import InfoEscrow from './InfoEscrow';
import EscrowPaymentPlan from './EscrowPaymentPlan';
import EscrowProduct from './EscrowProduct';
import { useNavigate } from 'react-router-dom';
import { ESCROW_CONTRACT } from '../../../../configs/path';
import PaymentInfo from './PaymentInfo';
import CustomerInfo from './CustomerInfo';
import UploadFileEscrowContract from '../uploadFileEscrowContract';

const { Title } = Typography;

interface ExtendedUploadFile extends UploadFile {
  key?: string;
  absoluteUrl?: string;
}

interface EscrowContractFormProps {
  form?: any;
  initialValues?: EscrowContract;
  onFinish?: (values: EscrowContract) => void;
  loading?: boolean;
}

const EscrowContractForm: React.FC<EscrowContractFormProps> = ({
  form: externalForm,
  initialValues,
  onFinish,
  loading = false,
}) => {
  const navigate = useNavigate();
  const [internalForm] = Form.useForm();
  const form = externalForm || internalForm;

  const orgchartType = Form.useWatch(['orgchart', 'type'], form);
  const depositAmount = Form.useWatch('depositAmount', form) || 0;
  const bankInfoOptions = Form.useWatch('bankAccount', form) || [];

  const [fileList, setFileList] = useState<ExtendedUploadFile[]>([]);
  // const [depositDates, setDepositDates] = useState<[Dayjs | null, Dayjs | null]>([dayjs(), null]);

  const [totalPaymentError, setTotalPaymentError] = useState<string | null>(null);
  const [installments, setInstallments] = useState<Installment[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<any[]>([]);
  const [selectedPropertyUnitIds, setSelectedPropertyUnitIds] = useState<string[]>([]);
  const [removedProductInfo, setRemovedProductInfo] = useState<{ salesProgramId: string; timestamp: number } | null>(
    null,
  );

  const [orgchartId, setOrgchartId] = useState<string | undefined>('');

  const { data: dataBanks, isLoading: isLoadingBanks } = useFetch<Bank[]>({
    queryKeyArrWithFilter: ['get-list-banks'],
    api: getBanks,
  });
  const banks = dataBanks?.data?.data || [];

  // Effect để populate initial values khi data được load
  useEffect(() => {
    if (initialValues && form) {
      const data = initialValues as any;

      const transformedValues = {
        ...initialValues,
        bankAccount: data.bank
          ? [
              {
                bankCode: data.bank.code,
                bankName: data.bank.name,
                accountNumber: data.bank.accountNumber,
                beneciary: data.bank.beneficiary,
              },
            ]
          : [],
        mainBankId: data.bank ? `${data.bank.code}-0` : undefined,
        mainBank: data.bank
          ? {
              code: data.bank.code,
              name: data.bank.name,
              accountNumber: data.bank.accountNumber,
              beneciary: data.bank.beneficiary,
            }
          : undefined,
        projectId: data.project?.id || data.projectId,
        depositSignedDate: data.depositSignedDate ? dayjs(data.depositSignedDate) : dayjs(),
        startDate: data.startDate ? dayjs(data.startDate) : null,
        expiredDate: data.expiredDate ? dayjs(data.expiredDate) : null,
        depositContract: {
          code: data.code,
        },
      };

      form.setFieldsValue(transformedValues);

      // Set orgchartId từ initial values nếu có
      if (data.orgchart?.id) {
        setOrgchartId(data.orgchart.id);
      }

      // Set installments nếu có và tính toán convertedAmount
      if (data.installments && Array.isArray(data.installments)) {
        setTimeout(() => {
          const currentDepositAmount = form.getFieldValue('depositAmount') || data.depositAmount || 0;
          const installmentsWithConvertedAmount = data.installments.map((inst: any) => ({
            ...inst,
            convertedAmount: calculateConvertedAmount(
              inst.value || 0,
              (inst.type as 'percent' | 'currency') || 'currency',
              currentDepositAmount,
            ),
          }));
          setInstallments(installmentsWithConvertedAmount);
        }, 0);
      }

      // Set files nếu có
      if (data.files && Array.isArray(data.files)) {
        const transformedFiles = data.files.map((file: any, index: number) => ({
          uid: file.uid || `file-${index}`,
          name: file.name || `file-${index}`,
          status: 'done',
          url: file.url,
          key: file.key || file.url,
          absoluteUrl: file.absoluteUrl || file.url,
        }));
        setFileList(transformedFiles);
      }

      // Set selectedPropertyUnitIds từ initial values
      if (data.propertyUnitIds && Array.isArray(data.propertyUnitIds)) {
        setSelectedPropertyUnitIds(data.propertyUnitIds);
      } else {
      }

      // Set deposit dates nếu có (hiện tại không sử dụng)
      // if (data.startDate && data.expiredDate) {
      //   setDepositDates([
      //     data.startDate ? dayjs(data.startDate) : null,
      //     data.expiredDate ? dayjs(data.expiredDate) : null,
      //   ]);
      // }

      // Set selectedProducts từ initialValues nếu có
      if (data.propertyUnits && Array.isArray(data.propertyUnits)) {
        setSelectedProducts(data.propertyUnits);

        // Set selectedPropertyUnitIds từ propertyUnits
        const productIds = data.propertyUnits.map((product: any) => product.id);
        setSelectedPropertyUnitIds(productIds);
      }
    }
  }, [initialValues, form, setSelectedProducts, setSelectedPropertyUnitIds]);

  useEffect(() => {
    const currentType = orgchartType || ORGCHART_TYPE.EXTERNAL;

    // Tính toán distributionChannel mặc định dựa trên loại đơn vị
    const defaultDistributionChannelCode = currentType === ORGCHART_TYPE.INTERNAL ? 10 : 13;

    // Chỉ set default values khi không có initialValues
    if (!initialValues) {
      form.setFieldsValue({
        orgchart: {
          type: currentType,
          id: undefined,
          name: undefined,
          code: undefined,
          bpID: undefined,
          taxCode: undefined,
          email: undefined,
        },
        // Reset field "Hợp tác với đơn vị bán hàng" khi thay đổi loại đơn vị
        orgchartPartnerId: undefined,
        // Set distributionChannel mặc định dựa trên loại đơn vị
        distributionChannel: defaultDistributionChannelCode,
      });

      // Reset state khi thay đổi loại đơn vị
      setOrgchartId(undefined);
    }
  }, [orgchartType, form, initialValues]);

  useEffect(() => {
    const currentMainBankId = form.getFieldValue('mainBankId');
    if (currentMainBankId) {
      const [bankCode, index] = currentMainBankId.split('-');
      const bankAccount = form.getFieldValue('bankAccount') || [];
      const selectedBank = bankAccount[parseInt(index)];

      // Chỉ clear mainBankId nếu tài khoản chính không còn tồn tại hoặc bankCode không khớp
      if (!selectedBank || selectedBank.bankCode !== bankCode) {
        form.setFieldsValue({
          mainBankId: undefined,
          mainBank: {
            code: '',
            name: '',
            accountNumber: '',
            beneciary: '',
          },
        });
      }
    }
  }, [bankInfoOptions, form]);

  // Khởi tạo giá trị mặc định cho distributionChannel
  useEffect(() => {
    const currentType = orgchartType || ORGCHART_TYPE.EXTERNAL;
    const defaultDistributionChannelCode = currentType === ORGCHART_TYPE.INTERNAL ? '10' : '13';

    const currentDistributionChannel = form.getFieldValue('distributionChannel');
    if (!currentDistributionChannel) {
      form.setFieldsValue({
        distributionChannel: defaultDistributionChannelCode,
      });
    }
  }, []);

  useEffect(() => {
    if (!depositAmount || depositAmount <= 0) {
      setInstallments([]);
      setTotalPaymentError(null);
    } else {
      const updatedInstallments = installments.map((inst: Installment) => ({
        ...inst,
        convertedAmount: calculateConvertedAmount(
          inst.value || 0,
          (inst.type as 'percent' | 'currency') || 'currency',
          depositAmount,
        ),
      }));
      setInstallments(updatedInstallments);
      validateTotalPayment(updatedInstallments);
    }
  }, [depositAmount]);

  const defaultBankOptions: BankOption[] = useMemo(
    () =>
      bankInfoOptions
        .filter((bank: Bank) => bank?.bankCode && bank?.bankName)
        .map((bank: Bank, index: number) => ({
          value: `${bank.bankCode}-${index}`,
          label: `${bank?.bankName || 'N/A'} - ${bank?.accountNumber || ''} - ${bank?.beneciary || ''}`,
          originalBankCode: bank.bankCode,
        })),
    [bankInfoOptions],
  );

  // Tính toán kênh phân phối mặc định dựa trên loại đơn vị
  const defaultDistributionChannel = useMemo(() => {
    const currentType = orgchartType || ORGCHART_TYPE.EXTERNAL;
    return currentType === ORGCHART_TYPE.INTERNAL
      ? { code: '10', name: 'Kênh bán nội bộ' }
      : { code: '13', name: 'Kênh bán bán lẻ' }; // Sửa tên cho đúng
  }, [orgchartType]);

  const handleRemoveBankAccount = (name: number) => {
    const currentMainBankId = form.getFieldValue('mainBankId');
    const bankAccount = form.getFieldValue('bankAccount');
    const removedBank = bankAccount[name];

    // Kiểm tra xem tài khoản bị xóa có phải là tài khoản chính không
    const isMainBankRemoved = currentMainBankId === `${removedBank?.bankCode}-${name}`;

    if (isMainBankRemoved) {
      // Nếu tài khoản chính bị xóa, đặt lại mainBankId và mainBank
      form.setFieldsValue({
        mainBankId: undefined,
        mainBank: {
          code: '',
          name: '',
          accountNumber: '',
          beneciary: '',
        },
      });
    } else if (currentMainBankId) {
      // Nếu tài khoản chính không bị xóa, cập nhật lại mainBankId để phản ánh chỉ số mới
      const [bankCode, currentIndex] = currentMainBankId.split('-');
      const currentIndexNum = parseInt(currentIndex);

      // Nếu chỉ số của tài khoản chính lớn hơn chỉ số bị xóa, giảm chỉ số đi 1
      if (currentIndexNum > name) {
        const newMainBankId = `${bankCode}-${currentIndexNum - 1}`;
        form.setFieldsValue({
          mainBankId: newMainBankId,
        });
      }
    }
  };

  const handleSelectBankInfo = (value: string) => {
    const selectedOption = defaultBankOptions.find(option => option.value === value);
    const selectedBank = bankInfoOptions.find((bank: Bank) => bank.bankCode === selectedOption?.originalBankCode);

    if (selectedBank) {
      const bankData = {
        code: selectedBank.bankCode || '',
        name: selectedBank.bankName || '',
        accountNumber: selectedBank.accountNumber || '',
        beneciary: selectedBank.beneciary || '',
      };

      form.setFieldsValue({
        mainBankId: value,
        mainBank: bankData,
      });
    } else {
      form.setFieldsValue({
        mainBankId: undefined,
        mainBank: {
          code: '',
          name: '',
          accountNumber: '',
          beneciary: '',
        },
      });
    }
  };

  // function để tính ngày đến hạn
  const calculateExpiredDate = (signedDate: dayjs.Dayjs, expiredDays: number): string => {
    if (!signedDate || !expiredDays || expiredDays <= 0) return '';
    return signedDate.add(expiredDays, 'day').format('DD/MM/YYYY');
  };

  // Hàm tính convertedAmount
  const calculateConvertedAmount = (value: number, type: 'percent' | 'currency', depositAmount: number) => {
    if (!value || !depositAmount || value <= 0 || depositAmount <= 0) return 0;

    let result: number;
    if (type === 'percent') {
      result = (value / 100) * depositAmount; // % → VNĐ
    } else {
      result = (value / depositAmount) * 100; // VNĐ → %
    }

    return Number(Math.round(result * 100) / 100);
  };

  // Hàm tính tổng số tiền thanh toán (chỉ tính các giá trị VND)
  const calculateTotalPayment = (installments: Installment[], depositAmount: number) => {
    return installments.reduce((total, inst) => {
      if (inst.type === 'currency' && inst.value) {
        return total + inst.value;
      } else if (inst.type === 'percent' && inst.value) {
        return total + (inst.value / 100) * depositAmount;
      }
      return total;
    }, 0);
  };

  // Hàm kiểm tra tổng số tiền thanh toán
  const validateTotalPayment = (installments: Installment[]) => {
    const total = calculateTotalPayment(installments, depositAmount);
    if (total > depositAmount) {
      setTotalPaymentError('Vui lòng nhập tổng giá trị thanh toán nhỏ hơn số tiền ký quỹ');
      return false;
    } else {
      setTotalPaymentError(null);
      return true;
    }
  };

  const handleEscrowContract = async (values: any) => {
    // Validate installments trước khi submit
    const invalidInstallments = installments.filter((inst, index) => {
      // Kiểm tra expiredDays
      if (!inst.expiredDays || inst.expiredDays <= 0) {
        return true;
      }

      // Kiểm tra giá trị thanh toán
      if (!inst.value || inst.value <= 0 || !Number.isInteger(Number(inst.value))) {
        return true;
      }

      // Kiểm tra thứ tự expiredDays
      if (index > 0) {
        const prevInstallment = installments[index - 1];
        if (prevInstallment.expiredDays && inst.expiredDays <= prevInstallment.expiredDays) {
          return true;
        }
      }

      return false;
    });

    if (invalidInstallments.length > 0) {
      // Set errors cho các installments không hợp lệ
      invalidInstallments.forEach(inst => {
        const installmentIndex = installments.findIndex(i => i.id === inst.id);

        if (!inst.expiredDays || inst.expiredDays <= 0) {
          form.setFields([
            {
              name: ['installments', installmentIndex, 'expiredDays'],
              errors: ['Vui lòng nhập số ngày thanh toán'],
            },
          ]);
        }
      });
      return;
    }

    // Xác định distributionChannel: sử dụng giá trị đã chọn hoặc giá trị mặc định
    const finalDistributionChannel = values.distributionChannel || defaultDistributionChannel.code;

    const payload = {
      projectId: values?.projectId,
      orgchart: {
        id: orgchartId || '',
        code: values.orgchart?.code || '',
        name: values.orgchart?.name || '',
        taxCode: values.orgchart?.taxCode,
        bpID: values.orgchart?.bpID || '',
        email: values.orgchart?.email,
        type: values.orgchart?.type,
      },
      orgchartPartnerId: values?.orgchartPartnerId,
      bank: {
        code: values.mainBank?.code || values.mainBank?.bankCode || '',
        name: values.mainBank?.name || values.mainBank?.bankName || '',
        accountNumber: values.mainBank?.accountNumber || '',
        beneficiary: values.mainBank?.beneciary || values.mainBank?.beneficiary || '',
      },
      depositForm: values.depositForm,
      depositTime: values.depositTime,
      depositAmount: values.depositAmount,
      salePolicyId: values.salePolicyId || '',
      // startDate: depositDates[0],
      // expiredDate: depositDates[1],
      type: values.type,
      POnumber: values.POnumber,
      distributionChannel: finalDistributionChannel,
      division: values.division ? Number(values.division) : undefined,
      propertyUnitIds: selectedPropertyUnitIds || [],
      installments: installments.map(inst => ({
        id: inst.id,
        name: inst.name,
        type: inst.type,
        value: inst.value,
        expiredDays: inst.expiredDays,
        description: inst.description,
        expiredDateType: inst.expiredDateType,
      })),
      files: fileList.map(file => ({
        name: file.name,
        url: file.url || '',
        absoluteUrl: file.absoluteUrl || '',
      })),
    };

    onFinish?.(payload);
  };

  const handleSelectOrgchartInternal = (values: any) => {
    setOrgchartId(values?.id || undefined);
    form.setFieldsValue({
      orgchart: {
        id: values?.id || undefined,
        name: values?.nameVN || undefined,
        code: values?.code || undefined,
        taxCode: values?.taxCode || undefined,
        email: values?.email || undefined,
      },
      // Reset đơn vị bán hàng khi thay đổi hoặc clear đơn vị chính
      orgchartPartnerId: undefined,
    });
  };

  const handleSelectOrgchartExternal = (values: any) => {
    setOrgchartId(values?.id || undefined);
    form.setFieldsValue({
      orgchart: {
        id: values?.id || undefined,
        code: values?.partnershipCode || undefined,
        name: values?.partnershipName || undefined,
        taxCode: values?.taxCode || undefined,
        bpID: values?.businessPartnerCode || undefined,
        email: values?.email || undefined,
      },

      // Reset đơn vị bán hàng khi thay đổi hoặc clear đơn vị chính
      orgchartPartnerId: undefined,
    });
  };

  const handleSelectDistributionChannel = (values: any) => {
    form.setFieldsValue({ distributionChannel: values?.code });
  };

  const handleSelectDivision = (values: any) => {
    form.setFieldsValue({ division: values?.code });
  };

  const handleSelectDepositProject = (values: any) => {
    form.setFieldsValue({ projectId: values?.id });
  };

  const handleSelectOrgchartPartnerInternal = (values: any) => {
    form.setFieldsValue({ orgchartPartnerId: values?.id });
  };

  const handleSelectOrgchartPartnerExternal = (values: any) => {
    form.setFieldsValue({ orgchartPartnerId: values?.id });
  };

  const handleSelectSalePolicy = (values: any) => {
    form.setFieldsValue({ salePolicyId: values?.id });
  };

  const handleCancel = useCallback(() => {
    if (form.isFieldsTouched()) {
      Modal.confirm({
        title: 'Xác nhận hủy',
        content: 'Dữ liệu chưa được lưu, bạn có chắc chắn muốn thoát khỏi trang không?',
        cancelText: 'Quay lại',
        okText: 'Đồng ý',
        onOk: () => {
          navigate(ESCROW_CONTRACT);
        },
        okButtonProps: {
          type: 'default',
        },
        cancelButtonProps: {
          type: 'primary',
        },
      });
    } else {
      navigate(ESCROW_CONTRACT);
    }
  }, [form, navigate]);

  return (
    <>
      <Form form={form} layout="vertical" onFinish={handleEscrowContract}>
        <Row gutter={32}>
          <Col span={14}>
            {/* Thông tin khách hàng */}
            <CustomerInfo
              form={form}
              orgchartType={orgchartType}
              orgchartId={orgchartId}
              handleSelectOrgchartInternal={handleSelectOrgchartInternal}
              handleSelectOrgchartExternal={handleSelectOrgchartExternal}
              handleSelectOrgchartPartnerInternal={handleSelectOrgchartPartnerInternal}
              handleSelectOrgchartPartnerExternal={handleSelectOrgchartPartnerExternal}
            />

            {/* Thông tin thanh toán */}
            <PaymentInfo
              form={form}
              banks={banks}
              isLoadingBanks={isLoadingBanks}
              defaultBankOptions={defaultBankOptions}
              handleRemoveBankAccount={handleRemoveBankAccount}
              handleSelectBankInfo={handleSelectBankInfo}
            />

            {/* Thông tin ký quỹ */}
            <InfoEscrow
              form={form}
              defaultDistributionChannel={defaultDistributionChannel}
              handleSelectDistributionChannel={handleSelectDistributionChannel}
              handleSelectDivision={handleSelectDivision}
              handleSelectDepositProject={handleSelectDepositProject}
              formatNumber={formatNumber}
            />

            {/* Sản phẩm ký quỹ */}
            <EscrowProduct
              selectedProducts={selectedProducts}
              setSelectedProducts={setSelectedProducts}
              selectedPropertyUnitIds={selectedPropertyUnitIds}
              setSelectedPropertyUnitIds={setSelectedPropertyUnitIds}
              removedProductInfo={removedProductInfo}
              setRemovedProductInfo={setRemovedProductInfo}
              initialValues={initialValues}
            />
          </Col>

          <Col span={10}>
            {/* Thông tin khác */}
            <Title level={5}>Thông tin phí mô giới</Title>
            <Form.Item label="Chính sách phí môi giới" name="salePolicyId">
              <SingleSelectLazy
                apiQuery={getListSalesPolicy}
                queryKey={['get-sale-policy']}
                keysLabel={'name'}
                placeholder="Chọn chính sách phí môi giới"
                handleSelect={handleSelectSalePolicy}
                moreParams={{ isActive: true, projectId: '' }}
                defaultValues={{
                  value: form.getFieldValue(['salePolicy', 'id']),
                  label: form.getFieldValue(['salePolicy', 'name']),
                }}
              />
            </Form.Item>

            <Title level={5}>Tài liệu đính kèm</Title>
            <Form.Item name="files">
              <UploadFileEscrowContract
                fileList={fileList}
                setFileList={setFileList}
                uploadPath="primary-contract/deposit-contract"
                size={25}
              />
            </Form.Item>
          </Col>
        </Row>
        <EscrowPaymentPlan
          installments={installments}
          setInstallments={setInstallments}
          form={form}
          handleKeyDownEnterNumber={handleKeyDownEnterNumber}
          totalPaymentError={totalPaymentError}
          setTotalPaymentError={setTotalPaymentError}
          depositAmount={depositAmount}
          calculateExpiredDate={calculateExpiredDate}
          validateTotalPayment={validateTotalPayment}
          calculateConvertedAmount={calculateConvertedAmount}
        />

        <div className="create-footer">
          <div className="button-create">
            <Button htmlType="button" onClick={handleCancel}>
              Hủy
            </Button>

            <Button type="primary" htmlType="submit" style={{ marginLeft: 12 }} disabled={loading}>
              Lưu
            </Button>
          </div>
        </div>
      </Form>
    </>
  );
};

export default EscrowContractForm;
