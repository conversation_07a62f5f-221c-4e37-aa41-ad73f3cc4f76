.modal-lead-detail {
  .ant-modal {
    width: 600px;
  }
  .ant-form {
    .ant-form-item {
      margin-bottom: 10px;
      .ant-form-item-label {
        text-align: left;
      }
    }
  }
  .ant-tabs-tabpane {
    height: 490px;
    overflow-y: auto;
  }
  .ant-timeline {
    padding: 12px 0;
    .timeline-item-lead {
      .time {
        color: #00000073;
      }
    }
  }
}

.text-history {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.timeline-dropdown {
  display: flex;
  justify-content: flex-end;
}

.timeline-section {
  margin-top: 32px;
}

.customer-name {
  font-size: 20px;
  font-weight: 600;
}

.customer-status {
  font-size: 12px;
}

.interaction-history-title {
  font-size: 16px;
  font-weight: 600;
}

.text-history-action {
  display: 'flex';
  justify-content: 'flex-end';
}

.content-history-debt-report {
  margin-top: 32px;
  .ant-space {
    margin-bottom: 24px;
  }
  .ant-timeline {
    margin-top: 32px;
  }
}
