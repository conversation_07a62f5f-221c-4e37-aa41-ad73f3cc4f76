import { deleteRequest, getRequest, postRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import { OwnershipCertificateConfig, TFilterOwnershipCertificateConfig } from '../../types/ownershipCertificateConfig';

export const getAllOwnershipCertificateConfig = async (params: TFilterOwnershipCertificateConfig) => {
  return await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v1}/ownership-certificate`,
    params,
  );
};

export const changeStatusOwnershipCertificateConfig = async (payload: { id: string }) => {
  return await putRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v1}/ownership-certificate/change-status`,
    payload,
  );
};

export const deleteOwnershipCertificateConfig = async (payload: { id?: string; reasonDelete?: string }) => {
  const response = await deleteRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v1}/ownership-certificate`,
    payload,
  );
  return response;
};

export const postCreateOwnershipCertificateConfig = async (data: OwnershipCertificateConfig) => {
  return await postRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v1}/ownership-certificate`,
    data,
  );
};

export const getByIdOwnershipCertificateConfig = async (id?: string) => {
  return await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v1}/ownership-certificate/${id}`,
  );
};

export const updateOwnershipCertificateConfig = async (data: OwnershipCertificateConfig) => {
  return await putRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v1}/ownership-certificate`,
    data,
  );
};

export const getAllProjects = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v1}/debt-commission-policy/get-dropdown-projects`,
    params as Record<string, unknown> | undefined,
  );
};
