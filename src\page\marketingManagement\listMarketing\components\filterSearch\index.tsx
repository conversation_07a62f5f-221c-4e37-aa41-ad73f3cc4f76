import { Input, InputProps } from 'antd';
import { useMemo } from 'react';

import './styles.scss';
import { useStoreMarketing } from '../../../store';
import { debounce } from 'lodash';
import { normalizeString } from '../../../../../utilities/regex';
import { SearchProps } from 'antd/lib/input';

function FilterSearch() {
  const { setFilter: setFilterParam } = useStoreMarketing();

  const debouncedHandleSearch = useMemo(
    () =>
      debounce((searchTerm: string) => {
        const normalizeSearchTerm = normalizeString(searchTerm);
        setFilterParam({ search: normalizeSearchTerm });
      }, 1000),
    [setFilterParam],
  );

  const handleChange: InputProps['onChange'] = event => {
    const searchTerm = event.target.value;
    debouncedHandleSearch(searchTerm);
  };

  const handleOnSearch: SearchProps['onSearch'] = value => {
    const normalizeSearchTerm = normalizeString(value);
    setFilterParam({ search: normalizeSearchTerm });
  };

  return (
    <>
      <Input.Search onChange={handleChange} onSearch={handleOnSearch} placeholder="Tìm kiếm" allowClear />
    </>
  );
}

export default FilterSearch;
