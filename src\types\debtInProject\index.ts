import { Dayjs } from 'dayjs';

export type TDebtAge = {
  name?: string;
  fromDay?: number | string;
  toDay?: number | string;
  interestRate?: number | string;
  isBadDebt?: boolean;
  isFinalRange?: boolean;
  key: string;
};

export type TDebtInterestPolicy = {
  id?: string;
  debtage: TDebtAge[];
  latePaymentFee: number;
  daysPerYear: number;
  delayDay: number;
  onlyWorkday: boolean;
  dueDateReminderDays: number;
  interestJobTime: string;
};

export type TReminder = {
  name: string;
  phase: string;
  remindDays: number | string;
  remindTime: string | null | Dayjs;
  smsTemplate?: string;
  emailTemplate?: string;
  debtAmountLimit: number | string;
  isActive?: number;
  key: string;
};

export interface IDebtInProject {
  _id: string;
  id: string;
  code: string;
  name: string;
  type: string;
  codePublic: string;
  namePublic: string;
  status: string;
  address: string;
  companyCode: string;
  companyName: string;
  createdBy: string | null;
  modifiedBy: string;
  createdDate: string;
  modifiedDate: string;
  setting: {
    debtage: TDebtAge[];
    debtReminder: TReminder[];
    dateBeforeFirstDebtRemind: unknown[];
    dateBeforeNextDebtRemind: unknown[];
    latePaymentFee: number;
    daysPerYear: number;
    delayDay: number;
    onlyWorkday: boolean;
    dueDateReminderDays: number;
    interestJobTime: string;
    isRetailCustomer: boolean;
    isManualReceipt: boolean;
    paymentTypes: unknown[];
    paymentPolicies: unknown[];
  };
}
