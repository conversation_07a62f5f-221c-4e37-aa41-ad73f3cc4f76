import React from 'react';
import { Form, InputNumber, Select, Button, Row, Col, Typography, Input } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { Installment } from '../../../../types/depositContract';
import TableComponent from '../../../../components/table';
import { v4 as uuidv4 } from 'uuid';
import { formatNumber } from '../../../../utilities/regex';

const { Option } = Select;
const { Title } = Typography;

interface EscrowPaymentPlanProps {
  installments: Installment[];
  setInstallments: React.Dispatch<React.SetStateAction<Installment[]>>;
  form: any;
  handleKeyDownEnterNumber: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  totalPaymentError: string | null;
  setTotalPaymentError: React.Dispatch<React.SetStateAction<string | null>>;
  depositAmount: number | null;
  calculateExpiredDate: (signedDate: dayjs.Dayjs, expiredDays: number) => string;
  validateTotalPayment: (installments: Installment[]) => boolean;
  calculateConvertedAmount: (value: number, type: 'currency' | 'percent', depositAmount: number) => number;
}

const EscrowPaymentPlan: React.FC<EscrowPaymentPlanProps> = ({
  installments,
  setInstallments,
  form,
  handleKeyDownEnterNumber,
  totalPaymentError,
  setTotalPaymentError,
  depositAmount,
  calculateExpiredDate,
  validateTotalPayment,
  calculateConvertedAmount,
}) => {
  const addInstallment = () => {
    if (!depositAmount || depositAmount <= 0) {
      setTotalPaymentError('Vui lòng thiết lập số tiền ký quỹ');
      return;
    }
    const newInstallment: Installment = {
      id: uuidv4(),
      name: '',
      type: 'currency',
      value: undefined,
      expiredDays: undefined,
      description: '',
      expiredDateType: 'numberDay',
    };
    setInstallments(prev => [...prev, newInstallment]);
    setTotalPaymentError(null);
  };

  const updateInstallment = (id: string, field: string, value: any) => {
    // Validation cho giá trị thanh toán
    if (field === 'value') {
      if (value !== null && value !== undefined) {
        const numValue = Number(value);
        if (numValue <= 0 || !Number.isInteger(numValue)) {
          return;
        }
      }
    }

    const newInstallments = installments.map(inst => {
      if (inst.id === id) {
        const updatedInst = { ...inst, [field]: value };

        // Cập nhật convertedAmount nếu thay đổi value hoặc type
        if (field === 'value' || field === 'type') {
          updatedInst.convertedAmount = calculateConvertedAmount(
            field === 'value' ? value || 0 : inst.value || 0,
            field === 'type' ? value : inst.type,
            depositAmount || 0,
          );
        }

        // Tính toán ngày đến hạn nếu có depositSignedDate và expiredDays
        if (field === 'expiredDays' && value && form.getFieldValue('depositSignedDate')) {
          const signedDate = dayjs(form.getFieldValue('depositSignedDate'));
          updatedInst.expiredDate = calculateExpiredDate(signedDate, value);
        }

        return updatedInst;
      }
      return inst;
    });

    setInstallments(newInstallments);
    if (field === 'value' || field === 'type') {
      validateTotalPayment(newInstallments);
    }
  };

  const deleteInstallment = (id: string) => {
    const newInstallments = installments.filter((inst: any) => inst.id !== id);
    setInstallments(newInstallments);
    validateTotalPayment(newInstallments);
  };

  const columnsInstallments = [
    {
      title: 'Tên đợt',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (_: string, record: any, index: number) => (
        <Form.Item noStyle name={['installments', index, 'name']}>
          <Input
            onChange={e => updateInstallment(record?.id, 'name', e.target.value)}
            placeholder="Nhập tên đợt thanh toán"
            maxLength={150}
          />
        </Form.Item>
      ),
    },
    {
      title: 'Giá trị thanh toán',
      dataIndex: 'value',
      key: 'value',
      width: 250,
      render: (_: string, record: any, index: number) => (
        <>
          <Select defaultValue={'TT số tiền ký quỹ'} style={{ width: '100%', marginBottom: 16 }}>
            <Option value="TT số tiền ký quỹ">TT số tiền ký quỹ</Option>
          </Select>
          <Form.Item noStyle name={['installments', index, 'value']}>
            <InputNumber
              maxLength={record.type === 'percent' ? 5 : 15}
              style={{ width: '100%' }}
              placeholder="Nhập giá trị thanh toán"
              min={1}
              max={record.type === 'percent' ? 100 : undefined}
              step={1}
              precision={0}
              formatter={(value: number | string | undefined) =>
                record.type === 'currency' && value !== undefined
                  ? `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                  : `${value}`
              }
              parser={(value: string | undefined) => {
                if (record.type === 'currency' && value) {
                  const parsed = parseInt(value.replace(/,/g, ''), 10);
                  return isNaN(parsed) ? 0 : parsed;
                }
                return value || '';
              }}
              onKeyDown={handleKeyDownEnterNumber}
              onChange={value => {
                const numValue = Number(value);
                if (value && numValue > 0 && Number.isInteger(numValue)) {
                  updateInstallment(record?.id, 'value', numValue);
                } else if (value === null || value === undefined || numValue === 0) {
                  updateInstallment(record?.id, 'value', null);
                }
              }}
              addonAfter={
                <Form.Item noStyle>
                  <Select
                    style={{ width: 80 }}
                    defaultValue={'currency'}
                    onChange={value => updateInstallment(record.id, 'type', value)}
                  >
                    <Option value="currency">VND</Option>
                    <Option value="percent">%</Option>
                  </Select>
                </Form.Item>
              }
            />
          </Form.Item>
        </>
      ),
    },
    {
      title: 'Thời hạn thanh toán',
      dataIndex: 'expiredDays',
      key: 'expiredDays',
      width: 200,
      render: (_: string, record: any, index: number) => (
        <>
          <Form.Item noStyle name={['installments', index, 'expiredDateType']}>
            <Select
              defaultValue={'numberDay'}
              onChange={value => updateInstallment(record.id, 'expiredDateType', value)}
              style={{ width: '100%', marginBottom: 16 }}
            >
              <Option value="numberDay">Từ sau ngày ký kết</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name={['installments', index, 'expiredDays']}
            style={{ margin: 0 }}
            rules={[
              {
                required: true,
                message: 'Vui lòng nhập số ngày thanh toán',
              },
              {
                validator: (_, value) => {
                  if (index > 0) {
                    const prevInstallment = installments[index - 1];
                    if (prevInstallment && prevInstallment.expiredDays && value <= prevInstallment.expiredDays) {
                      return Promise.reject('Vui lòng nhập số ngày nhiều hơn số ngày TT của đợt liền trước');
                    }
                  }

                  return Promise.resolve();
                },
              },
            ]}
          >
            <InputNumber
              style={{ width: '100%' }}
              onChange={value => updateInstallment(record?.id, 'expiredDays', value)}
              placeholder="Nhập số ngày thanh toán"
              suffix="Ngày"
              min={1}
              precision={0}
            />
          </Form.Item>
        </>
      ),
    },
    // {
    //   title: 'Ngày đến hạn dự kiến',
    //   dataIndex: 'expiredDate',
    //   key: 'expiredDate',
    //   width: 200,
    //   render: (_: string, record: any) => (
    //     <div style={{ textAlign: 'center', padding: '8px 0' }}>
    //       {record.expiredDate ? (
    //         <span style={{ color: '#1890ff', fontWeight: 500 }}>{record.expiredDate}</span>
    //       ) : (
    //         <span style={{ color: '#999', fontStyle: 'italic' }}>Chưa tính được</span>
    //       )}
    //     </div>
    //   ),
    // },
    {
      title: 'Mô tả',
      dataIndex: 'description',
      key: 'description',
      width: 230,
      render: (_: string, record: any, index: number) => (
        <Form.Item noStyle name={['installments', index, 'description']}>
          <Input
            onChange={e => updateInstallment(record?.id, 'description', e.target.value)}
            placeholder="Nhập mô tả tiến độ thanh toán"
            maxLength={255}
          />
        </Form.Item>
      ),
    },

    {
      title: 'Quy đổi tỉ lệ/ số tiền tương ứng',
      dataIndex: 'convertedAmount',
      key: 'convertedAmount',
      width: 280,
      render: (_: string, record: any) => (
        <InputNumber
          maxLength={record.type === 'percent' ? 5 : 15}
          style={{ width: '100%' }}
          placeholder="Số tiền TT"
          min={0}
          max={record.type === 'percent' ? 100 : undefined}
          step={record.type === 'percent' ? 0.01 : 1}
          precision={2}
          formatter={formatNumber}
          parser={(value: string | undefined) =>
            record.type === 'currency' && value ? parseFloat(value.replace(/,/g, '')) : value || ''
          }
          onKeyDown={handleKeyDownEnterNumber}
          addonAfter={
            <Form.Item noStyle>
              <Select
                style={{ width: 80 }}
                defaultValue={'currency'}
                value={record.type === 'percent' ? 'currency' : 'percent'}
              >
                <Option value="currency">VND</Option>
                <Option value="percent">%</Option>
              </Select>
            </Form.Item>
          }
          disabled
          value={record.convertedAmount || 0}
        />
      ),
    },
    {
      title: 'Thao tác',
      key: 'action',
      width: 100,
      align: 'center' as const,
      render: (_: string, record: any) => (
        <Button type="link" danger onClick={() => deleteInstallment(record.id)} size="small">
          Xóa
        </Button>
      ),
    },
  ];

  return (
    <div>
      {/* Kế hoạch thanh toán ký quỹ */}
      <Title level={5}>Kế hoạch thanh toán ký quỹ</Title>
      {totalPaymentError && <div style={{ color: 'red', marginBottom: 16 }}>{totalPaymentError}</div>}
      <Button type="dashed" onClick={addInstallment} icon={<PlusOutlined />} style={{ marginBottom: 16 }}>
        Thêm đợt TT
      </Button>

      <Row gutter={16}>
        <Col span={24}>
          <TableComponent
            columns={columnsInstallments}
            queryKeyArr={['']}
            rowKey="id"
            dataSource={installments}
            isPagination={false}
            className="table-installments"
          />
        </Col>
      </Row>
    </div>
  );
};

export default EscrowPaymentPlan;
