import { Typography } from 'antd';

const { Text } = Typography;
import { TableColumnsType } from 'antd';
import { EPropertyAttributeStatus, IPropertyAttribute } from '../../../types/propertyAttribute';
import dayjs from 'dayjs';
import { Link } from 'react-router-dom';
import { PROPERTY_ATTRIBUTES_MANAGEMENT } from '../../../configs/path';

export const columnsPropertyAttribute: TableColumnsType<IPropertyAttribute> = [
  {
    title: 'Tên thuộc tính',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: 'Dự án áp dụng',
    dataIndex: 'project',
    key: 'project',
    render: project => project.name,
  },
  {
    title: 'CTBH',
    dataIndex: 'saleProgram',
    key: 'saleProgram',
    render: saleProgram => saleProgram.name,
  },
  {
    title: 'Trạng thái',
    dataIndex: 'status',
    key: 'status',
    align: 'center',
    render: (status: IPropertyAttribute['status']) => (
      <Text type={status === EPropertyAttributeStatus.Enabled ? 'success' : 'danger'}>
        {status === EPropertyAttributeStatus.Enabled ? 'Kích hoạt' : 'Vô hiệu'}
      </Text>
    ),
  },
  {
    title: 'Ngày tạo',
    dataIndex: 'createdAt',
    key: 'createdAt',
    render: (date: string) => dayjs(date).format('DD/MM/YYYY HH:mm'),
  },
  {
    title: 'Người tạo',
    dataIndex: 'createdBy',
    key: 'createdBy',
  },
  {
    title: '',
    dataIndex: 'action',
    key: 'action',
    align: 'right',
    width: 100,
    render: (_, record) => <Link to={`${PROPERTY_ATTRIBUTES_MANAGEMENT}/${record.id}`}>Xem</Link>,
  },
];
