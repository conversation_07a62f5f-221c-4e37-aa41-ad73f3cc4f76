enum OrgchartEnum {
  INTERNAL = 'INTERNAL',
  EXTERNAL = 'EXTERNAL',
}

enum DepositContractFormEnum {
  NO_DEPOSIT = 'NO_DEPOSIT',
}

interface Bank {
  code?: string;
  name?: string;
  accountNumber?: string;
  beneficiary?: string;
}

export interface Installment {
  id?: string;
  name?: string;
  paymentType?: string;
  type?: string;
  value?: number;
  expiredDateType?: string;
  exactDays?: string;
  expiredDays?: number;
  expiredDate?: string;
  description?: string;
  convertedAmount?: number;
  transactionSuccessful?: boolean;
}

export interface EscrowContract {
  _id?: string;
  id?: string;
  code?: string;
  projectId?: string;
  project?: {
    id?: string;
    name?: string;
    code?: string;
  };
  orgchart?: {
    id?: string;
    email?: string;
    type?: OrgchartEnum;
    name?: string;
    code?: string;
    bpID?: string;
    taxCode?: string;
  };
  orgchartPartnerId?: string;
  bank?: Bank;
  depositSignedDate?: string;
  depositForm?: DepositContractFormEnum;
  depositTime?: number;
  depositAmount?: number;
  salePolicyId?: string;
  propertyUnitIds?: string[];
  installments?: Installment[];
  status?: number;
  softDelete?: boolean;
  createdDate?: string;
  modifiedDate?: string;
  createdBy?: string;
  modifiedBy?: string;
  name?: string;
  imageUrl?: string;
}

export interface DistributionChannel {
  code: string;
  createdDate: string;
  id: string;
  lastUpdate: string;
  name: string;
  status: number;
}

export interface Division {
  id?: string;
  name?: string;
  code?: string;
}

export interface DepositContract {
  id?: string;
}

export interface OrgchartPartner {
  code: string;
  id: string;
  nameVN: string;
  taxCode: string;
}

export interface SalePolicy {
  code: string;
  createdBy: {
    email: string;
    userName: string;
    fullName: string;
  };
  createdDate: string;
  id: string;
  isActive: number;
  isUpdateStatus: boolean;
  listProjectId: string[];
  modifiedDate: string;
  name: string;
  period: string;
  periodFrom: string;
  periodTo: string;
  projectName: string;
  year: number;
}
