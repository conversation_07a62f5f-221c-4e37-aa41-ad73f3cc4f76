import { Dayjs } from 'dayjs';
import { TUpdatedBy } from '../common/common';

export type TUser = {
  id: string;
  name: string;
};

export type TPos = {
  id: string;
  name: string;
  code: string;
  type: string;
  parentId: string;
  address: string;
};

export type TProject = {
  id: string;
  code: string;
  name: string;
  type: string;
};

export type TNote = {
  id: string;
  title: string;
  detail: string;
};

export type TCustomer = {
  address: string;
  gender: string;
  name: string;
  phone: string;
};

export type TStaff = {
  email?: string;
  name?: string;
  id: string;
  username: string;
  fullName: string;
};

export type THistory = {
  createdBy: TStaff;
  createdDate: string;
  status: string;
};

export type TDebtReportHistory = {
  customer: TCustomer;
  histories: THistory[];
  status: string;
};
export type IDebReport = {
  id: string;
  code: string;
  createdDate: string;
  exploitStatus: 'done' | 'assign' | 'processing' | 'cancel'; // Thêm các trạng thái khác n<PERSON>u có
  importedBy: TUser;
  modifiedBy: string;
  name: string;
  phone: string;
  pos: TPos;
  takeCare: TakeCare;
  updatedDate: string;
  visiblePhone: string | null;
  modifiedByObj: TUpdatedBy;
  debtHistory: TDebtReportHistory;
  contractName?: string;
  customerCode?: string;
};
export type TakeCare = {
  email: string;
  id: string;
  name: string;
  phone: string;
};

export type TCreateLeadSource = {
  id: string | undefined;
  name: string;
  code: string;
  createdDate?: string | Dayjs | null;
  updatedDate?: string | Dayjs | null;
  updatedBy: string;
  createdBy: string;
  configNumber?: number;
};
export interface TManualDeliver {
  contractIds: string[];
  employee?: string;
}
