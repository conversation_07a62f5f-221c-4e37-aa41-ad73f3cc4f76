import { Button, Form, Tabs, TabsProps } from 'antd';
import dayjs from 'dayjs';
import ModalComponent from '../../../components/modal';
import { showConfirmCancelModal } from '../../../components/modal/specials/ConfirmCancelModal';
import { FORMAT_DATE_API } from '../../../constants/common';
import { useCreateField } from '../../../hooks';
import { useStoreOwnershipCertificateConfig } from '../storeOwnershipCertificateConfig';
import { formatTimeFrameToSubmit, validateForms } from '../utilities';
import { postCreateOwnershipCertificateConfig } from '../../../service/ownershipCertificateConfig';
import NotificationTab from '../components/notificationTab';
import GeneralTab from '../components/generalTab';

interface Props {
  onCancel: () => void;
}

const CreateOwnershipCertificateConfig = (props: Props) => {
  const { onCancel } = props;
  const {
    openModalCreate,
    dataForEligibles,
    dataCerInProcesss,
    dataForCerReadyHandovers,
    setDataForEligibles,
    setDataCerInProcesss,
    setDataForCerReadyHandovers,
  } = useStoreOwnershipCertificateConfig();
  const [formGen] = Form.useForm();
  const [formNoti] = Form.useForm();

  const { mutateAsync, isPending } = useCreateField({
    keyOfListQuery: ['list-ownership-certificate'],
    apiQuery: postCreateOwnershipCertificateConfig,
    isMessageError: false,
  });

  const handleSubmit = async () => {
    try {
      const formResult = await validateForms(formGen, formNoti);
      if (!formResult) return;

      const { valuesGen, valuesNoti } = formResult;

      const formatExpectedDate = {
        expectedStartDate: dayjs(valuesGen?.expectedDateRange?.[0]).format(FORMAT_DATE_API),
        expectedEndDate: dayjs(valuesGen?.expectedDateRange?.[1]).format(FORMAT_DATE_API),
        expectedDateRange: undefined,
      };
      const formatTimeFrames = formatTimeFrameToSubmit(valuesGen?.timeFrames);

      const values = {
        ...valuesGen,
        ...valuesNoti,
        ...formatExpectedDate,
        itemsForEligible: dataForEligibles,
        itemsCerInProcess: dataCerInProcesss,
        itemsForCerReadyHandover: dataForCerReadyHandovers,
        status: valuesGen?.status ? 1 : 2,
        accountingConfirm: valuesGen?.accountingConfirm ? 1 : 2,
        paymentPercent: valuesGen?.paymentPercent && parseFloat(valuesGen?.paymentPercent),
        ...formatTimeFrames,
        timeFrames: undefined,
        isActive: valuesGen?.isActive ? 1 : 2,
      };
      const res = await mutateAsync(values);
      if (res?.data?.statusCode === '0') {
        onCancel();
        formGen.resetFields();
        formNoti.resetFields();
        setDataForEligibles([]);
        setDataCerInProcesss([]);
        setDataForCerReadyHandovers([]);
      }
    } catch (err) {
      console.error('Validation failed:', err);
    }
  };

  const items: TabsProps['items'] = [
    {
      label: 'Thông tin chung',
      key: 'general-info',
      children: <GeneralTab form={formGen} />,
    },
    {
      label: 'Thông báo',
      key: 'notification',
      children: <NotificationTab form={formNoti} />,
    },
  ];

  return (
    <ModalComponent
      rootClassName="wrapper-create-of-delivery-config"
      open={openModalCreate}
      onCancel={() => {
        if (!formGen.isFieldsTouched() && !formNoti.isFieldsTouched()) {
          onCancel();
          return;
        }
        showConfirmCancelModal({
          onConfirm: onCancel,
        });
      }}
      afterClose={() => {
        formGen.resetFields();
        formNoti.resetFields();
        setDataForEligibles([]);
        setDataCerInProcesss([]);
        setDataForCerReadyHandovers([]);
      }}
      title="Tạo mới thiết lập bàn giao"
      destroyOnClose
      footer={[
        <Button key="submit" type="primary" onClick={handleSubmit} loading={isPending}>
          Lưu
        </Button>,
      ]}
    >
      <Tabs items={items} />
    </ModalComponent>
  );
};

export default CreateOwnershipCertificateConfig;
