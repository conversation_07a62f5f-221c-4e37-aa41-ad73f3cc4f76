import { Radio, RadioChangeEvent, Space } from 'antd';
import { useState } from 'react';
import PenaltyInterest from './penaltyInterest';
import DebtReminder from './DebtReminder';
import { useSearchParams } from 'react-router-dom';
import { useCheckPermissions } from '../../hooks';
import { PERMISSION_PROPERTY_PROJECT_DEBT } from '../../constants/permissions/propertyProjectDebt';

type TTabDebt = 'penaltyInterest' | 'debtReminder';

const DebtInProject = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const params = Object.fromEntries([...searchParams]);
  const { projectDebtPenaltyUpdate, projectDebtReminderUpdate } = useCheckPermissions(PERMISSION_PROPERTY_PROJECT_DEBT);
  const [tab, setTab] = useState<TTabDebt>((params?.tabDebt as TTabDebt) || 'penaltyInterest');

  const handleTabChange = (e: RadioChangeEvent) => {
    const value = e.target.value as TTabDebt;
    setTab(value);
    setSearchParams({ ...params, tabDebt: value });
  };

  return (
    <div>
      <Space style={{ marginBottom: 24 }}>
        <Radio.Group value={tab} onChange={handleTabChange}>
          <Radio.Button value="penaltyInterest">Chính sách lãi phạt</Radio.Button>
          <Radio.Button value="debtReminder">Cấu hình nhắc nợ</Radio.Button>
        </Radio.Group>
      </Space>
      <div style={{ display: tab === 'penaltyInterest' ? 'block' : 'none' }}>
        {projectDebtPenaltyUpdate && <PenaltyInterest />}
      </div>
      <div style={{ display: tab === 'debtReminder' ? 'block' : 'none' }}>
        {projectDebtReminderUpdate && <DebtReminder />}
      </div>
    </div>
  );
};

export default DebtInProject;
