import { Button, Form, Modal, Select } from 'antd';
import React from 'react';
import { useFetch, useUpdateField } from '../../../../../hooks';
import './styles.scss';
import { IDebReport, TStaff } from '../../../../../types/debtReport';
import { getListOfStaff, manualDeliver } from '../../../../../service/debtReport';
import { useForm } from 'antd/lib/form/Form';

interface ModalAssignDebtReportProps {
  isOpen: boolean;
  onClose: () => void;
  selectedRows: IDebReport[];
}

function ModalAssignDebtReport(props: ModalAssignDebtReportProps) {
  const { isOpen, selectedRows, onClose } = props;

  const [form] = useForm();

  const [assignedStaff, setAssignedStaff] = React.useState<string>();

  const { data: dataStaff } = useFetch<TStaff[]>({
    queryKeyArrWithFilter: ['get-list-staff'],
    api: getListOfStaff,
  });

  const { mutateAsync: mutateManualDeliver } = useUpdateField({
    apiQuery: manualDeliver,
    keyOfListQuery: ['get-debt-reports'],
    messageSuccess: 'Phân bổ thành công',
  });

  const listStaffs = dataStaff?.data?.data;

  const handleManualDeliver = React.useCallback(async () => {
    const contractIds: string[] = [];

    selectedRows?.map(o => {
      contractIds?.push(o?.id as string);
      return o;
    });

    const res = await mutateManualDeliver({ contractIds: contractIds, employee: assignedStaff });
    if (res?.data?.statusCode === '0') {
      onClose();
    }
  }, [assignedStaff, mutateManualDeliver, onClose, selectedRows]);

  return (
    <Modal
      className="modal-share-care"
      open={isOpen}
      title="Phân bổ nhân viên thu hồi công nợ"
      width={380}
      onCancel={onClose}
      confirmLoading
      maskClosable={false}
      footer={
        <Button type="primary" onClick={handleManualDeliver}>
          Phân bổ
        </Button>
      }
    >
      <Form form={form}>
        <Form.Item name="staff">
          <Select
            showSearch
            allowClear
            onChange={value => {
              setAssignedStaff(value);
            }}
            optionFilterProp="label"
            options={listStaffs?.map(item => {
              return {
                listStaff: { id: item?.id, name: item?.name, email: item?.email },
                value: item.id,
                label: item?.name,
                name: item?.name,
              };
            })}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
}

export default ModalAssignDebtReport;
