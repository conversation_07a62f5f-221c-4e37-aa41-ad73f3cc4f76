import { getRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import { TDebtInterestPolicy, TReminder } from '../../types/debtInProject';

export const getPenaltyInterestInProject = async (id?: string) => {
  return await getRequest(`${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/project/debt-penalty/${id}`);
};

export const putPenaltyInterestInProject = async (payload: TDebtInterestPolicy) => {
  return await putRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/project/debt-penalty/${payload?.id}`,
    payload,
  );
};

export const getDebtRemainderInProject = async (id?: string) => {
  return await getRequest(`${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/project/debt-reminder/${id}`);
};

export const putDebtRemainderInProject = async (payload: { id?: string; debtReminder: TReminder }) => {
  return await putRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/project/debt-reminder/${payload?.id}`,
    payload,
  );
};
